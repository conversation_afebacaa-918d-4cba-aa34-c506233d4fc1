import { useEffect, useState } from "react"
import { getImageUrl } from "@/app/lib/file-utils"

interface AppointmentReferenceImageProps {
  url: string
  index: number
}

export function AppointmentReferenceImage({ url, index }: AppointmentReferenceImageProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [imgSrc, setImgSrc] = useState<string | null>(null)
  const [fallbackAttempted, setFallbackAttempted] = useState(false)

  // Process the URL to ensure it's properly formatted
  useEffect(() => {
    if (!url) {
      console.error(`No URL provided for reference image ${index + 1}`);
      setImageError(true);
      return;
    }

    // Use the thumbnail API for faster loading
    // Size 100x100 is perfect for the small thumbnails in the admin dashboard
    const thumbnailUrl = `/api/thumbnail?url=${encodeURIComponent(url)}&width=100&height=100&quality=80`;

    console.log(`Reference image ${index + 1} original URL:`, url);
    console.log(`Reference image ${index + 1} thumbnail URL:`, thumbnailUrl);

    // Reset states when URL changes
    setImageError(false);
    setFallbackAttempted(false);
    setImgSrc(thumbnailUrl);

    // Preload the image
    const img = new Image();
    img.src = thumbnailUrl;
  }, [url, index]);

  // Handle image loading error
  const handleImageError = () => {
    console.error(`Error loading thumbnail image ${index + 1}:`, url);

    // If we haven't tried the fallback yet, try a different approach
    if (!fallbackAttempted) {
      setFallbackAttempted(true);

      // Try a different approach - add a timestamp to bust cache
      const timestamp = Date.now();
      const cacheBustedUrl = `/api/thumbnail?url=${encodeURIComponent(url)}&width=100&height=100&quality=80&t=${timestamp}&bypass_cache=true`;

      console.log(`Trying cache-busted thumbnail URL for image ${index + 1}:`, cacheBustedUrl);
      setImgSrc(cacheBustedUrl);
    } else {
      // If the thumbnail API fails, fall back to the regular proxy
      const proxyUrl = getImageUrl(url, {
        isAppointmentImage: true,
        cacheBust: true
      });

      console.log(`Thumbnail failed, trying proxy URL for image ${index + 1}:`, proxyUrl);
      setImgSrc(proxyUrl);

      // If this also fails, the onError handler will be called again
      // and we'll show the error state
      setImageError(true);
    }
  };

  return (
    <div className="relative w-12 h-12 overflow-hidden rounded-md border bg-gray-100 hover:opacity-80 transition-opacity group inline-block">
      <div className="w-full h-full">
        {!imageError && imgSrc && (
          <img
            src={imgSrc}
            alt={`Reference image ${index + 1}`}
            className="w-full h-full object-cover"
            loading="eager" // Use eager loading for these small thumbnails
            onLoad={() => setImageLoaded(true)}
            onError={handleImageError}
          />
        )}

        {(imageError || !imgSrc) && (
          <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500 text-xs">
            Image {index + 1}
          </div>
        )}
      </div>

      <div className="absolute inset-0 bg-black/30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
        <span className="text-white text-xs font-medium">
          Ref {index + 1}
        </span>
      </div>
    </div>
  )
}
