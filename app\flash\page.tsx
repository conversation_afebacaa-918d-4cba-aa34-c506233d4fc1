"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Search, DollarSign } from "lucide-react"
import { toast } from "sonner"
import { ImageModal } from "@/components/ui/image-modal"
import { Button } from "@/components/ui/button"
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { formatIDR } from '@/lib/utils'

// Display interface for frontend use
interface FlashTattooDisplay {
  _id: string
  title: string
  category: string
  imageUrl: string
  price: number
  size: string
  uploadDate: string
}

interface FlashDesign {
  _id: string
  name: string
  description: string
  price: number
  imageUrl: string
  categories: string[]
  size: string
  createdAt: string
  updatedAt: string
}

interface Category {
  _id: string
  name: string
  slug: string
  description?: string
  order: number
}

export default function FlashTattoos() {
  const [designs, setDesigns] = useState<FlashDesign[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [designsRes, categoriesRes] = await Promise.all([
          fetch('/api/flash'),
          fetch('/api/categories')
        ])

        if (!designsRes.ok || !categoriesRes.ok) {
          throw new Error('Failed to fetch data')
        }

        const [designsData, categoriesData] = await Promise.all([
          designsRes.json(),
          categoriesRes.json()
        ])

        setDesigns(designsData)
        setCategories(categoriesData)
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const filteredDesigns = designs.filter(design => {
    const matchesCategory = !selectedCategory || design.categories.includes(selectedCategory)
    const matchesSearch = !searchQuery ||
      design.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      design.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Input
          type="text"
          placeholder="Search designs..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-md mb-4"
        />
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === null ? "default" : "outline"}
            onClick={() => setSelectedCategory(null)}
          >
            All
          </Button>
          {categories.map((category) => (
            <Button
              key={category._id}
              variant={selectedCategory === category.slug ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.slug)}
            >
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {filteredDesigns.length === 0 ? (
        <div className="text-center text-gray-500">
          No designs found matching your criteria
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredDesigns.map((design) => (
            <div
              key={design._id}
              className="bg-white rounded-lg shadow-md overflow-hidden"
            >
              <div className="relative aspect-square">
                <Image
                  src={design.imageUrl}
                  alt={design.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">{design.name}</h3>
                <p className="text-gray-600 text-sm mb-2">{design.description}</p>
                <p className="text-primary font-semibold">${design.price}</p>
                <p className="text-sm text-gray-500 mt-2">Size: {design.size}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
