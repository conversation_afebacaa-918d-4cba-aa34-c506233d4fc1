import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { ObjectId } from 'mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    console.log('Categories API called')
    // Allow public access for GET requests to categories
    // Only check auth if there's a token provided
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')

    if (token) {
      console.log('Token provided:', token.substring(0, 10) + '...')
      const authResult = await verifyAuth(token)
      if (!authResult.success) {
        console.log('Auth failed')
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        )
      }
      console.log('Auth successful')
    } else {
      console.log('No token provided, proceeding as public access')
    }

    console.log('Getting database connection')
    const db = await getDb()
    console.log('Database connection successful')

    console.log('Fetching categories from collection:', COLLECTIONS.CATEGORIES)
    const categories = await db.collection(COLLECTIONS.CATEGORIES)
      .find({})
      .sort({ order: 1 })
      .toArray()
    console.log('Categories fetched, count:', categories.length)

    // Format categories for response
    const formattedCategories = categories.map(category => ({
      _id: category._id.toString(),
      name: category.name,
      slug: category.slug,
      description: category.description,
      order: category.order
    }))

    return NextResponse.json(formattedCategories)
  } catch (error) {
    console.error('Error in categories GET:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      )
    }

    const db = await getDb()

    // Get the highest order number
    const lastCategory = await db.collection(COLLECTIONS.CATEGORIES)
      .findOne({}, { sort: { order: -1 } })
    const order = (lastCategory?.order || 0) + 1

    // Create the new category object
    const newCategory = {
      name,
      description,
      slug: name.toLowerCase().replace(/\s+/g, '-'),
      order,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: {
        userId: authResult.userId,
        role: authResult.role,
        timestamp: new Date().toISOString()
      }
    }

    const result = await db.collection(COLLECTIONS.CATEGORIES).insertOne(newCategory)

    // Return the complete category object
    return NextResponse.json({
      _id: result.insertedId.toString(),
      ...newCategory
    })
  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { name, description } = body

    // Log the received data for debugging
    console.log('Updating category with ID:', id)
    console.log('Received data:', body)

    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      )
    }

    const db = await getDb()
    const result = await db.collection(COLLECTIONS.CATEGORIES).updateOne(
      { _id: new ObjectId(id) },
      {
        $set: {
          name,
          description,
          slug: name.toLowerCase().replace(/\s+/g, '-'),
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Category updated successfully'
    })
  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    const db = await getDb()

    // Check if category is in use
    const designsUsingCategory = await db.collection(COLLECTIONS.DESIGNS).findOne({
      category: new ObjectId(id)
    })

    if (designsUsingCategory) {
      return NextResponse.json(
        { error: 'Cannot delete category that is in use' },
        { status: 400 }
      )
    }

    const result = await db.collection(COLLECTIONS.CATEGORIES).deleteOne({
      _id: new ObjectId(id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Category deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    )
  }
}