import { ObjectId, Collection, WithId, Document } from 'mongodb'
import type { 
  Appointment, 
  AppointmentStatus,
  Category,
  BaseWork,
  FlashTattoo,
  CustomDesign,
  CoverUp,
  Size,
  User,
  Settings
} from './schema.d'

export const COLLECTIONS = {
  users: 'users',
  appointments: 'appointments',
  designs: 'designs',
  sizes: 'sizes',
  tags: 'tags',
  categories: 'categories',
  gallery: 'gallery'
} as const

export const appointmentSchema = {
  name: { type: 'string', required: true, minLength: 2, maxLength: 100 },
  email: { type: 'string', required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
  phone: { type: 'string', required: true, pattern: /^\+?[\d\s-()]{10,}$/ },
  date: { type: 'date', required: true },
  time: { type: 'string', required: true, pattern: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
  design: { type: 'string', required: false, maxLength: 500 },
  description: { type: 'string', required: false, maxLength: 1000 },
  status: { type: 'string', required: true, enum: ['pending', 'confirmed', 'cancelled'] },
  createdAt: { type: 'date', required: true },
  updatedAt: { type: 'date', required: true }
}

export const AppointmentModel = {
  collection: COLLECTIONS.appointments,
  schema: appointmentSchema,
  indexes: [
    { key: { date: 1 } },
    { key: { status: 1 } },
    { key: { email: 1 } },
    { key: { date: 1, status: 1 } },
    { key: { email: 1, status: 1 } }
  ],
  methods: {
    async findAvailableSlots(date: Date, collection: Collection): Promise<Appointment[]> {
      const appointments = await collection.find({
        date: { 
          $gte: new Date(date.setHours(0, 0, 0, 0)),
          $lt: new Date(date.setHours(23, 59, 59, 999))
        },
        status: { 
          $in: ['confirmed', 'pending'] 
        }
      }).toArray() as WithId<Document>[]
      return appointments.map(doc => ({
        _id: doc._id.toString(),
        name: doc.name as string,
        email: doc.email as string,
        phone: doc.phone as string,
        date: doc.date as Date,
        time: doc.time as string,
        design: doc.design as string | undefined,
        description: doc.description as string | undefined,
        status: doc.status as AppointmentStatus,
        createdAt: doc.createdAt as Date,
        updatedAt: doc.updatedAt as Date
      }))
    },
    async findUpcomingAppointments(collection: Collection): Promise<Appointment[]> {
      const appointments = await collection.find({
        date: { $gte: new Date() },
        status: 'confirmed'
      }).sort({ date: 1 }).toArray() as WithId<Document>[]
      return appointments.map(doc => ({
        _id: doc._id.toString(),
        name: doc.name as string,
        email: doc.email as string,
        phone: doc.phone as string,
        date: doc.date as Date,
        time: doc.time as string,
        design: doc.design as string | undefined,
        description: doc.description as string | undefined,
        status: doc.status as AppointmentStatus,
        createdAt: doc.createdAt as Date,
        updatedAt: doc.updatedAt as Date
      }))
    },
    async findPastAppointments(collection: Collection): Promise<Appointment[]> {
      const appointments = await collection.find({
        date: { $lt: new Date() },
        status: 'confirmed'
      }).sort({ date: -1 }).toArray() as WithId<Document>[]
      return appointments.map(doc => ({
        _id: doc._id.toString(),
        name: doc.name as string,
        email: doc.email as string,
        phone: doc.phone as string,
        date: doc.date as Date,
        time: doc.time as string,
        design: doc.design as string | undefined,
        description: doc.description as string | undefined,
        status: doc.status as AppointmentStatus,
        createdAt: doc.createdAt as Date,
        updatedAt: doc.updatedAt as Date
      }))
    },
    async findPendingAppointments(collection: Collection): Promise<Appointment[]> {
      const appointments = await collection.find({
        status: 'pending'
      }).sort({ date: 1 }).toArray() as WithId<Document>[]
      return appointments.map(doc => ({
        _id: doc._id.toString(),
        name: doc.name as string,
        email: doc.email as string,
        phone: doc.phone as string,
        date: doc.date as Date,
        time: doc.time as string,
        design: doc.design as string | undefined,
        description: doc.description as string | undefined,
        status: doc.status as AppointmentStatus,
        createdAt: doc.createdAt as Date,
        updatedAt: doc.updatedAt as Date
      }))
    },
    async findCancelledAppointments(collection: Collection): Promise<Appointment[]> {
      const appointments = await collection.find({
        status: 'cancelled'
      }).sort({ date: -1 }).toArray() as WithId<Document>[]
      return appointments.map(doc => ({
        _id: doc._id.toString(),
        name: doc.name as string,
        email: doc.email as string,
        phone: doc.phone as string,
        date: doc.date as Date,
        time: doc.time as string,
        design: doc.design as string | undefined,
        description: doc.description as string | undefined,
        status: doc.status as AppointmentStatus,
        createdAt: doc.createdAt as Date,
        updatedAt: doc.updatedAt as Date
      }))
    }
  },
  static: {
    async createAppointment(data: Omit<Appointment, '_id' | 'createdAt' | 'updatedAt'>, collection: Collection): Promise<Appointment> {
      const now = new Date()
      const result = await collection.insertOne({
        ...data,
        createdAt: now,
        updatedAt: now
      })
      return {
        _id: result.insertedId.toString(),
        ...data,
        createdAt: now,
        updatedAt: now
      }
    },
    async updateAppointment(id: string, data: Partial<Omit<Appointment, '_id' | 'createdAt' | 'updatedAt'>>, collection: Collection): Promise<Appointment | null> {
      const result = await collection.findOneAndUpdate(
        { _id: new ObjectId(id) },
        { 
          $set: { 
            ...data,
            updatedAt: new Date()
          }
        },
        { returnDocument: 'after' }
      )
      if (!result) return null
      return {
        _id: result._id.toString(),
        name: result.name as string,
        email: result.email as string,
        phone: result.phone as string,
        date: result.date as Date,
        time: result.time as string,
        design: result.design as string | undefined,
        description: result.description as string | undefined,
        status: result.status as AppointmentStatus,
        createdAt: result.createdAt as Date,
        updatedAt: result.updatedAt as Date
      }
    },
    async deleteAppointment(id: string, collection: Collection): Promise<boolean> {
      const result = await collection.deleteOne({ _id: new ObjectId(id) })
      return result.deletedCount > 0
    },
    async getAppointment(id: string, collection: Collection): Promise<Appointment | null> {
      const result = await collection.findOne({ _id: new ObjectId(id) })
      if (!result) return null
      return {
        _id: result._id.toString(),
        name: result.name as string,
        email: result.email as string,
        phone: result.phone as string,
        date: result.date as Date,
        time: result.time as string,
        design: result.design as string | undefined,
        description: result.description as string | undefined,
        status: result.status as AppointmentStatus,
        createdAt: result.createdAt as Date,
        updatedAt: result.updatedAt as Date
      }
    }
  }
}

export interface GalleryImage {
  _id: string
  imageUrl: string
  title?: string
  category?: string
  tags?: string[]
  isFeatured: boolean
  featuredOrder?: number
  createdAt: Date
  updatedAt: Date
} 