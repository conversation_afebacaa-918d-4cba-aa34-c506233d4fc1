"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Mail, Phone, MapPin, Clock, Instagram, Facebook } from "lucide-react"
import { STUDIO_INFO } from "@/lib/config"
import Link from "next/link"

export default function ContactPage() {
  const businessHours = STUDIO_INFO.getFormattedBusinessHours()

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Get in touch with our team for consultations, questions, or to schedule your appointment
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
        <div className="lg:col-span-1 space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Studio Information</CardTitle>
              <CardDescription>Find us at our location or reach out directly</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-start space-x-4">
                <MapPin className="h-5 w-5 text-muted-foreground mt-1" />
                <div>
                  <p className="font-medium">Location</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.name}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.address}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.area}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.city}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.region} {STUDIO_INFO.location.postalCode}</p>
                  <Link 
                    href={STUDIO_INFO.location.googleMapsUrl}
                    target="_blank"
                    className="text-primary hover:underline inline-block mt-2"
                  >
                    View on Google Maps
                  </Link>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <Phone className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Phone/WhatsApp</p>
                  <Link 
                    href={`tel:${STUDIO_INFO.phone}`}
                    className="text-muted-foreground hover:text-primary"
                  >
                    {STUDIO_INFO.phone}
                  </Link>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Email</p>
                  <Link 
                    href={`mailto:${STUDIO_INFO.email}`}
                    className="text-muted-foreground hover:text-primary"
                  >
                    {STUDIO_INFO.email}
                  </Link>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <Clock className="h-5 w-5 text-muted-foreground mt-1" />
                <div>
                  <p className="font-medium">Business Hours</p>
                  {businessHours.map(({ day, hours }) => (
                    <p key={day} className="text-muted-foreground">
                      {day}: {hours}
                    </p>
                  ))}
                </div>
              </div>

              <div className="pt-4 border-t">
                <p className="font-medium mb-3">Follow Us</p>
                <div className="flex space-x-4">
                  <Link 
                    href={STUDIO_INFO.social.instagram}
                    target="_blank"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <Instagram className="h-5 w-5" />
                  </Link>
                  <Link 
                    href={STUDIO_INFO.social.facebook}
                    target="_blank"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <Facebook className="h-5 w-5" />
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <iframe
                src={STUDIO_INFO.location.googleMapsEmbedUrl}
                width="100%"
                height="300"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="rounded-lg"
              />
            </CardContent>
          </Card>
        </div>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Send us a Message</CardTitle>
            <CardDescription>
              Fill out the form below and we'll get back to you as soon as possible
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input id="firstName" placeholder="Your first name" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input id="lastName" placeholder="Your last name" />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" placeholder="Your email" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input id="phone" type="tel" placeholder="Your phone number" />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input id="subject" placeholder="What is this regarding?" />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  placeholder="Tell us about your tattoo idea or any questions you have"
                  className="min-h-[150px]"
                />
              </div>
              
              <Button size="lg" className="w-full">Send Message</Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
