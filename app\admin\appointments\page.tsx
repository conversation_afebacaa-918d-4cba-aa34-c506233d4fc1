"use client"

import { useState, useEffect, useMemo } from "react"
import { useRout<PERSON> } from "next/navigation"
import { format, isToday, isPast, parseISO } from "date-fns"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { toast } from "sonner"
import { PlusCircle, Calendar, Eye, PenSquare, CheckCircle } from "lucide-react"
import Link from "next/link"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Pencil } from "lucide-react"
import { AppointmentReferenceImage } from "@/components/AppointmentReferenceImage"

interface Appointment {
  _id: string
  fullName: string
  email: string
  phone: string
  date: string
  time: string
  serviceType: {
    _id: string
    name: string
    slug: string
  }
  size: {
    _id: string
    name: string
    description: string
  }
  description?: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  media?: Array<{
    url: string
    previewUrl: string
  }>
  createdAt: string
  updatedAt: string
}

export default function AdminAppointments() {
  const router = useRouter()
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled'>('all')
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false)
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false)
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null)
  const [updatedAppointmentId, setUpdatedAppointmentId] = useState<string | null>(null)

  useEffect(() => {
    fetchAppointments()
  }, [])

  // Get today's appointments with memoization to prevent unnecessary recalculations
  const todaysAppointments = useMemo(() =>
    appointments.filter(appointment => {
      // Use the date directly without adjustment
      return isToday(new Date(appointment.date))
    }),
    [appointments] // Only recalculate when appointments change
  )

  // Filter appointments based on active tab with memoization
  const filteredAppointments = useMemo(() =>
    appointments.filter(appointment => {
      if (activeTab === 'all') return true
      return appointment.status === activeTab
    }),
    [appointments, activeTab] // Only recalculate when appointments or activeTab change
  )

  const fetchAppointments = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/admin/appointments', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (!response.ok) throw new Error('Failed to fetch appointments')
      const data = await response.json()
      setAppointments(data)
    } catch (error) {
      toast.error('Failed to load appointments')
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (appointmentId: string, newStatus: Appointment['status']) => {
    // Store the previous appointments state for rollback if needed
    const previousAppointments = appointments

    try {
      // Optimistically update the UI immediately
      setAppointments(prevAppointments =>
        prevAppointments.map(appointment =>
          appointment._id === appointmentId
            ? { ...appointment, status: newStatus }
            : appointment
        )
      )

      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch(`/api/admin/appointments/${appointmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update appointment')
      }

      const data = await response.json()

      // Update the appointments list with the returned appointment data
      setAppointments(prevAppointments =>
        prevAppointments.map(appointment =>
          appointment._id === appointmentId
            ? { ...appointment, ...data.appointment }
            : appointment
        )
      )

      toast.success(`Appointment ${newStatus} successfully`)
    } catch (error) {
      // Revert to the previous state if the API call fails
      setAppointments(previousAppointments)
      console.error('Error updating appointment status:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update appointment status')
    }
  }

  const handleConfirmClick = (appointment: Appointment) => {
    setSelectedAppointment(appointment)
    setConfirmDialogOpen(true)
  }

  const handleCancelClick = (appointment: Appointment) => {
    setSelectedAppointment(appointment)
    setCancelDialogOpen(true)
  }

  const handleCompleteClick = (appointment: Appointment) => {
    setSelectedAppointment(appointment)
    setCompleteDialogOpen(true)
  }

  const handleConfirmAppointment = async () => {
    if (selectedAppointment) {
      setConfirmDialogOpen(false) // Close dialog immediately
      setSelectedAppointment(null)
      await handleStatusUpdate(selectedAppointment._id, 'confirmed')
    }
  }

  const handleCancelAppointment = async () => {
    if (selectedAppointment) {
      setCancelDialogOpen(false) // Close dialog immediately
      setSelectedAppointment(null)
      await handleStatusUpdate(selectedAppointment._id, 'cancelled')
    }
  }

  const handleCompleteAppointment = async () => {
    if (selectedAppointment) {
      setCompleteDialogOpen(false) // Close dialog immediately
      setSelectedAppointment(null)
      await handleStatusUpdate(selectedAppointment._id, 'completed')
    }
  }

  // Reset updated appointment ID after a short delay
  useEffect(() => {
    if (updatedAppointmentId) {
      const timer = setTimeout(() => {
        setUpdatedAppointmentId(null)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [updatedAppointmentId])

  const isAppointmentStarted = (date: string, time: string) => {
    // Use the date directly without adjustment
    const dateObj = new Date(date)
    // Format the date to ISO string and extract the date part
    const dateStr = dateObj.toISOString().split('T')[0]
    // Create the appointment date time
    const appointmentDateTime = parseISO(`${dateStr}T${time}`)
    return isPast(appointmentDateTime)
  }

  const getStatusBadgeColor = (status: Appointment['status']) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'confirmed':
        return 'default'
      case 'completed':
        return 'outline'
      case 'cancelled':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const AppointmentTable = ({ appointments }: { appointments: Appointment[] }) => (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date & Time</TableHead>
            <TableHead>Client</TableHead>
            <TableHead>Service</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Reference Images</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {appointments.map((appointment) => (
            <TableRow key={appointment._id}>
              <TableCell>
                <div className="flex flex-col">
                  <span>{format(new Date(appointment.date), 'MMM d, yyyy')}</span>
                  <span className="text-muted-foreground">{appointment.time}</span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span>{appointment.fullName}</span>
                  <span className="text-muted-foreground">{appointment.email}</span>
                </div>
              </TableCell>
              <TableCell>{appointment.serviceType.name}</TableCell>
              <TableCell>{appointment.size.name}</TableCell>
              <TableCell>
                <Badge variant={getStatusBadgeColor(appointment.status)}>
                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                </Badge>
              </TableCell>
              <TableCell>
                {appointment.media && appointment.media.length > 0 && (
                  <div className="flex gap-2">
                    {appointment.media.map((image, index) => (
                      <AppointmentReferenceImage
                        key={image.url}
                        url={image.url}
                        index={index}
                      />
                    ))}
                  </div>
                )}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    asChild
                  >
                    <Link href={`/admin/appointments/${appointment._id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    asChild
                  >
                    <Link href={`/admin/appointments/${appointment._id}/edit`}>
                      <Pencil className="h-4 w-4" />
                    </Link>
                  </Button>
                  {appointment.status === 'pending' && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleConfirmClick(appointment)}
                    >
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Appointments</h1>
        <Button asChild>
          <Link href="/admin/appointments/new">
            <PlusCircle className="w-4 h-4 mr-2" />
            New Appointment
          </Link>
        </Button>
      </div>

      <div className="space-y-6">
        {todaysAppointments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Today&apos;s Appointments</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                  <TabsTrigger value="confirmed">Confirmed</TabsTrigger>
                  <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
                  <TabsTrigger value="completed">Completed</TabsTrigger>
                </TabsList>

                <TabsContent value="all">
                  <AppointmentTable appointments={todaysAppointments} />
                </TabsContent>
                <TabsContent value="pending">
                  <AppointmentTable appointments={todaysAppointments.filter(a => a.status === 'pending')} />
                </TabsContent>
                <TabsContent value="confirmed">
                  <AppointmentTable appointments={todaysAppointments.filter(a => a.status === 'confirmed')} />
                </TabsContent>
                <TabsContent value="cancelled">
                  <AppointmentTable appointments={todaysAppointments.filter(a => a.status === 'cancelled')} />
                </TabsContent>
                <TabsContent value="completed">
                  <AppointmentTable appointments={todaysAppointments.filter(a => a.status === 'completed')} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>All Appointments</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="w-full" onValueChange={(value) => setActiveTab(value as typeof activeTab)}>
              <TabsList className="mb-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="confirmed">Confirmed</TabsTrigger>
                <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                <AppointmentTable appointments={filteredAppointments} />
              </TabsContent>
              <TabsContent value="pending">
                <AppointmentTable appointments={appointments.filter(a => a.status === 'pending')} />
              </TabsContent>
              <TabsContent value="confirmed">
                <AppointmentTable appointments={appointments.filter(a => a.status === 'confirmed')} />
              </TabsContent>
              <TabsContent value="cancelled">
                <AppointmentTable appointments={appointments.filter(a => a.status === 'cancelled')} />
              </TabsContent>
              <TabsContent value="completed">
                <AppointmentTable appointments={appointments.filter(a => a.status === 'completed')} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Status update dialogs */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to confirm this appointment? An email will be sent to notify the client.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAppointment}
              className="bg-green-500 hover:bg-green-600"
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel this appointment? An email will be sent to notify the client.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>No, keep it</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelAppointment}
              className="bg-red-500 hover:bg-red-600"
            >
              Yes, cancel it
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={completeDialogOpen} onOpenChange={setCompleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Complete Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to mark this appointment as completed? An email will be sent to thank the client.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCompleteAppointment}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Complete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}