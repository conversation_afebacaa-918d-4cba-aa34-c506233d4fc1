import { COLLECTIONS } from './collections'

export type AppointmentStatus = 'pending' | 'confirmed' | 'cancelled'

export interface Appointment {
  _id: string
  name: string
  email: string
  phone: string
  date: Date
  time: string
  design?: string
  description?: string
  status: AppointmentStatus
  createdAt: Date
  updatedAt: Date
  createdBy?: string
  updatedBy?: string
}

export interface Category {
  _id: string
  name: string
  slug: string
  description?: string
  order: number
  createdAt: Date
  updatedAt: Date
}

export interface BaseWork {
  _id: string
  name: string
  description: string
  price: number
  imageUrl: string
  categories: string[] // Array of category slugs
  size: string
  createdAt: Date
  updatedAt: Date
  isFeatured: boolean
  featuredOrder?: number
}

export interface FlashTattoo extends BaseWork {
  // Additional flash-specific fields can be added here
}

export interface CustomDesign extends BaseWork {
  status: 'pending' | 'approved' | 'rejected'
  clientName: string
  clientEmail: string
  additionalNotes?: string
}

export interface CoverUp extends BaseWork {
  beforeImageUrl: string
  afterImageUrl: string
  complexity: 'low' | 'medium' | 'high'
  estimatedHours: number
}

export interface Size {
  _id: string
  name: string
  description?: string
  order: number
  createdAt: Date
  updatedAt: Date
}

export interface User {
  _id: string
  email: string
  name: string
  role: 'admin' | 'user'
  createdAt: Date
  updatedAt: Date
}

export interface Settings {
  _id: string
  key: string
  value: any
  updatedAt: Date
}

export const appointmentSchema: {
  name: { type: string; required: boolean; minLength: number; maxLength: number }
  email: { type: string; required: boolean; pattern: RegExp }
  phone: { type: string; required: boolean; pattern: RegExp }
  date: { type: string; required: boolean }
  time: { type: string; required: boolean; pattern: RegExp }
  design: { type: string; required: boolean; maxLength: number }
  description: { type: string; required: boolean; maxLength: number }
  status: { type: string; required: boolean; enum: AppointmentStatus[] }
  createdAt: { type: string; required: boolean }
  updatedAt: { type: string; required: boolean }
}

export const AppointmentModel: {
  collection: typeof COLLECTIONS.APPOINTMENTS
  schema: typeof appointmentSchema
  indexes: Array<{ key: Record<string, number> }>
  methods: {
    findAvailableSlots(date: Date, collection: any): Promise<Appointment[]>
    findUpcomingAppointments(collection: any): Promise<Appointment[]>
    findPastAppointments(collection: any): Promise<Appointment[]>
    findPendingAppointments(collection: any): Promise<Appointment[]>
    findCancelledAppointments(collection: any): Promise<Appointment[]>
  }
  static: {
    createAppointment(data: Omit<Appointment, '_id' | 'createdAt' | 'updatedAt'>, collection: any): Promise<Appointment>
    updateAppointment(id: string, data: Partial<Omit<Appointment, '_id' | 'createdAt' | 'updatedAt'>>, collection: any): Promise<Appointment | null>
    deleteAppointment(id: string, collection: any): Promise<boolean>
    getAppointment(id: string, collection: any): Promise<Appointment | null>
  }
} 