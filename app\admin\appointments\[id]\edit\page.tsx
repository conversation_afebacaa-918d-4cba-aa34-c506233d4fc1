"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { format, parseISO } from "date-fns"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AppointmentCalendar } from "@/components/appointment-calendar"
import { SelectWithDescription } from "@/components/ui/select-with-description"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Loader2 } from "lucide-react"
import { ImageGrid } from "@/components/ImageGrid"

interface Category {
  _id: string
  name: string
  description?: string
  slug: string
}

interface Size {
  _id: string
  name: string
  description?: string
}

interface MediaFile {
  url: string
  previewUrl: string
}

interface Appointment {
  _id: string
  fullName: string
  email: string
  phone: string
  date: string
  time: string
  serviceType: {
    _id: string
    name: string
    slug: string
  }
  size: {
    _id: string
    name: string
    description: string
  }
  description?: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  media?: Array<{
    url: string
    previewUrl: string
  }>
}

export default function EditAppointmentPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [appointment, setAppointment] = useState<Appointment | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [sizes, setSizes] = useState<Size[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [uploadingImage, setUploadingImage] = useState(false)
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    serviceType: "",
    size: "",
    description: "",
    date: "",
    time: undefined as string | undefined,
    status: "" as Appointment["status"],
    media: [] as MediaFile[]
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch appointment details
        const appointmentResponse = await fetch(`/api/admin/appointments/${params.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`
          }
        })

        if (!appointmentResponse.ok) {
          throw new Error("Failed to fetch appointment details")
        }

        const appointmentData = await appointmentResponse.json()
        setAppointment(appointmentData)
        setFormData({
          fullName: appointmentData.fullName,
          email: appointmentData.email,
          phone: appointmentData.phone,
          serviceType: appointmentData.serviceType._id,
          size: appointmentData.size._id,
          description: appointmentData.description || "",
          date: appointmentData.date,
          time: appointmentData.time,
          status: appointmentData.status,
          media: appointmentData.media || []
        })

        // Fetch categories
        const categoriesResponse = await fetch('/api/categories')
        if (!categoriesResponse.ok) throw new Error('Failed to fetch categories')
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData)

        // Fetch sizes
        const sizesResponse = await fetch('/api/sizes')
        if (!sizesResponse.ok) throw new Error('Failed to fetch sizes')
        const sizesData = await sizesResponse.json()
        setSizes(sizesData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error("Failed to load appointment details")
        router.push("/admin/appointments")
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [params.id, router])

  const checkTimeSlotAvailability = async (date: string, time: string, excludeId: string) => {
    try {
      const formattedDate = format(parseISO(date), 'yyyy-MM-dd')
      const response = await fetch(`/api/appointments/available-slots?date=${formattedDate}`)

      if (!response.ok) {
        throw new Error('Failed to check time slot availability')
      }

      const data = await response.json()
      const bookedSlots = data.bookedSlots || []

      // Check if the time is booked by another appointment (excluding current appointment)
      const isBooked = bookedSlots.some((slot: string) => {
        const appointment = data.appointments?.find((apt: any) => apt.time === slot)
        return appointment && appointment._id !== excludeId && appointment.status !== 'cancelled'
      })

      return !isBooked
    } catch (error) {
      console.error('Error checking time slot availability:', error)
      throw new Error('Failed to verify time slot availability')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const selectedCategory = categories.find(cat => cat._id === formData.serviceType)
      if (!selectedCategory) {
        throw new Error("Please select a valid service type")
      }

      const selectedSize = sizes.find(size => size._id === formData.size)
      if (!selectedSize) {
        throw new Error("Please select a valid size")
      }

      // If changing from cancelled to confirmed, check time slot availability
      if (appointment?.status === 'cancelled' && formData.status === 'confirmed') {
        const isAvailable = await checkTimeSlotAvailability(formData.date, formData.time!, params.id)
        if (!isAvailable) {
          throw new Error("This time slot is no longer available. Please select a different time.")
        }
      }

      const response = await fetch(`/api/admin/appointments/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`
        },
        body: JSON.stringify({
          ...formData,
          serviceType: {
            _id: selectedCategory._id,
            name: selectedCategory.name,
            slug: selectedCategory.slug
          },
          size: {
            _id: selectedSize._id,
            name: selectedSize.name,
            description: selectedSize.description
          },
          media: formData.media
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update appointment")
      }

      toast.success("Appointment updated successfully")
      router.push("/admin/appointments")
    } catch (error) {
      console.error("Error updating appointment:", error)
      toast.error(error instanceof Error ? error.message : "Failed to update appointment")
    } finally {
      setSaving(false)
    }
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setUploadingImage(true)

    try {
      const uploadedMedia: MediaFile[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // Validate file type
        if (!file.type.startsWith('image/')) {
          toast.error(`${file.name} is not an image file`)
          continue
        }

        // Create preview
        const previewUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(file)
        })

        // Upload file
        const uploadFormData = new FormData()
        uploadFormData.append('file', file)
        uploadFormData.append('type', 'appointments')

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: uploadFormData
        })

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`)
        }

        const data = await response.json()
        uploadedMedia.push({
          url: data.filePath,
          previewUrl
        })
      }

      setFormData(prev => ({
        ...prev,
        media: [...prev.media, ...uploadedMedia]
      }))

      toast.success('Images uploaded successfully')
    } catch (error) {
      console.error('Error uploading images:', error)
      toast.error('Failed to upload one or more images')
    } finally {
      setUploadingImage(false)
    }
  }

  const handleRemoveImage = async (index: number) => {
    try {
      const imageToRemove = formData.media[index];

      // Delete from server/MinIO bucket
      const response = await fetch(`/api/upload?path=${encodeURIComponent(imageToRemove.url)}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete image from server');
      }

      // Update form state
      setFormData(prev => ({
        ...prev,
        media: prev.media.filter((_, i) => i !== index)
      }));

      toast.success('Image removed successfully');
    } catch (error) {
      console.error('Error removing image:', error);
      toast.error('Failed to remove image');
    }
  }

  if (loading || !appointment) {
    return (
      <div className="container mx-auto py-16 px-4">
        <div className="text-center">Loading appointment details...</div>
      </div>
    )
  }

  const appointmentDate = formData.date ? parseISO(formData.date) : undefined

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" /> Back
        </Button>
        <h1 className="text-2xl font-bold">Edit Appointment</h1>
      </div>

      {loading ? (
        <div>Loading...</div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                value={formData.fullName}
                onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Service Type</Label>
              <SelectWithDescription
                items={categories}
                value={formData.serviceType}
                onValueChange={(value) => setFormData({ ...formData, serviceType: value })}
                placeholder="Select service type"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Size</Label>
              <SelectWithDescription
                items={sizes}
                value={formData.size}
                onValueChange={(value) => setFormData({ ...formData, size: value })}
                placeholder="Select size"
              />
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value: Appointment["status"]) => setFormData({ ...formData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={4}
            />
          </div>

          <div className="space-y-2">
            <Label>Appointment Date & Time</Label>
            <AppointmentCalendar
              selectedDate={appointmentDate}
              selectedTime={formData.time}
              onDateChange={(date) => date && setFormData({
                ...formData,
                date: date.toISOString(),
                time: undefined
              })}
              onTimeChange={(time) => setFormData({ ...formData, time })}
            />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Reference Images</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4">
                <Label htmlFor="images">Upload Images</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="images"
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    disabled={uploadingImage}
                    className="w-full"
                  />
                  {uploadingImage && <Loader2 className="h-4 w-4 animate-spin" />}
                </div>
                {formData.media.length > 0 && (
                  <ImageGrid
                    images={formData.media}
                    onRemove={handleRemoveImage}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      )}
    </div>
  )
}