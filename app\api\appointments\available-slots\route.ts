import { NextResponse } from 'next/server'
import clientPromise from '@/lib/mongodb'
import { Document } from 'mongodb'

export const dynamic = 'force-dynamic';

interface Appointment extends Document {
  time: string
  date: Date | string
  status: string
}

// Helper function to normalize time format
function normalizeTimeFormat(time: string): string {
  if (!time) return '';

  // Convert to uppercase and ensure space before AM/PM
  let normalizedTime = time.trim().toUpperCase().replace(/([AMP])M/, ' $1M')

  // Remove any extra spaces
  normalizedTime = normalizedTime.replace(/\s+/g, ' ')

  // Handle cases without AM/PM
  if (!normalizedTime.includes('AM') && !normalizedTime.includes('PM')) {
    const [hours, minutes] = normalizedTime.split(':')
    const hour = parseInt(hours)
    const period = hour >= 12 ? 'PM' : 'AM'
    const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    normalizedTime = `${hour12}:${minutes || '00'} ${period}`
  }

  // Ensure minutes are included
  if (!normalizedTime.includes(':')) {
    normalizedTime = normalizedTime.replace(/ ([AMP]M)/, ':00 $1')
  }

  // Final cleanup to ensure consistent format
  const [timepart, meridiem] = normalizedTime.split(' ')
  const [hours, minutes] = timepart.split(':')
  const hour = parseInt(hours)
  const min = minutes || '00'

  // Ensure proper 12-hour format
  return `${hour}:${min} ${meridiem}`
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')

    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      )
    }

    const client = await clientPromise
    const db = client.db()

    // We'll use the date string directly instead of parsing it

    // Extract just the date part (YYYY-MM-DD) and create date objects
    const dateString = date.split('T')[0] // Get just the YYYY-MM-DD part

    // Create a range that spans only the exact day requested
    // This is important to avoid showing bookings from adjacent days
    const startOfDay = new Date(`${dateString}T00:00:00.000Z`)
    const endOfDay = new Date(`${dateString}T23:59:59.999Z`)

    console.log('Searching for appointments between:', {
      startOfDay: startOfDay.toISOString(),
      endOfDay: endOfDay.toISOString(),
      queryDate: date
    })

    // First, let's check all appointments for this date regardless of status
    // Use a more targeted query to find appointments for this specific date
    const allAppointments = await db.collection<Appointment>('appointments').find({
      $or: [
        // For Date objects
        {
          date: {
            $gte: startOfDay,
            $lte: endOfDay
          }
        },
        // For string dates that contain the date string
        {
          date: { $type: 'string', $regex: dateString }
        }
      ]
    }).toArray()

    // Then filter them in JavaScript to find the ones for our date
    const filteredAppointments = allAppointments.filter(apt => {
      // Check if the date is a Date object
      if (apt.date instanceof Date) {
        // Extract just the date part for comparison (ignore time)
        const aptDate = new Date(apt.date);
        aptDate.setHours(0, 0, 0, 0);

        const compareDate = new Date(dateString);
        compareDate.setHours(0, 0, 0, 0);

        // Compare only the date parts (year, month, day)
        return aptDate.getFullYear() === compareDate.getFullYear() &&
               aptDate.getMonth() === compareDate.getMonth() &&
               aptDate.getDate() === compareDate.getDate();
      }

      // Check if the date is a string that contains our date string
      if (typeof apt.date === 'string') {
        // Extract just the date part (YYYY-MM-DD) for exact matching
        const aptDateStr = apt.date.split('T')[0];
        return aptDateStr === dateString;
      }

      return false;
    })

    console.log('Filtered appointments for date:', filteredAppointments.map(apt => ({
      time: apt.time,
      date: apt.date instanceof Date ? apt.date.toISOString() : apt.date,
      status: apt.status
    })))

    console.log('All appointments found:', allAppointments.length)
    console.log('Looking for appointments on date:', dateString)

    // Now get both confirmed and pending appointments from our filtered list
    const appointments = filteredAppointments.filter(apt =>
      apt.status === 'confirmed' || apt.status === 'pending' || !apt.status
    )

    console.log('Filtered appointments (confirmed/pending):', appointments.map(apt => ({
      time: apt.time,
      status: apt.status,
      date: apt.date instanceof Date ? apt.date.toISOString() : apt.date
    })))

    // Extract booked time slots and normalize format
    const bookedSlots = appointments.map(appointment => {
      const normalizedTime = normalizeTimeFormat(appointment.time)
      console.log('Normalizing time:', {
        original: appointment.time,
        normalized: normalizedTime,
        status: appointment.status
      })
      return normalizedTime
    })

    console.log('Final booked slots:', bookedSlots)

    // Add the raw appointments to the response for debugging
    const appointmentsData = filteredAppointments.map(apt => ({
      time: apt.time,
      date: apt.date instanceof Date ? apt.date.toISOString() : String(apt.date),
      status: apt.status || 'unknown'
    }))

    console.log('Sending response with booked slots:', { bookedSlots, appointmentsCount: appointments.length })

    return NextResponse.json({
      bookedSlots,
      debug: {
        appointments: appointmentsData,
        date: date,
        dateString: dateString,
        startOfDay: startOfDay.toISOString(),
        endOfDay: endOfDay.toISOString(),
        totalAppointments: allAppointments.length,
        filteredAppointments: filteredAppointments.length,
        bookedAppointments: appointments.length
      }
    })
  } catch (error) {
    console.error('Error fetching available slots:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available slots' },
      { status: 500 }
    )
  }
}