import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { sendEmail } from '@/app/lib/email'
import { verifyAuth } from '@/lib/auth'
import { ObjectId } from 'mongodb'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    // Verify admin authorization
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)
    
    if (!authResult.success || !authResult.userId) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const { userId, email, adminPassword } = await request.json()

    if (!userId || !email || !adminPassword) {
      return NextResponse.json(
        { error: 'User ID, email, and admin password are required' },
        { status: 400 }
      )
    }

    const db = await connectToDatabase()
    
    // Verify the admin's password
    const adminUser = await db.collection(COLLECTIONS.USERS).findOne({
      _id: new ObjectId(authResult.userId)
    })

    if (!adminUser) {
      return NextResponse.json(
        { error: 'Admin user not found' },
        { status: 404 }
      )
    }

    // Verify admin role
    if (adminUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can reset passwords' },
        { status: 403 }
      )
    }

    // Verify admin password
    const isPasswordValid = await bcrypt.compare(adminPassword, adminUser.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid admin password' },
        { status: 401 }
      )
    }

    // Find the target user
    const targetUser = await db.collection(COLLECTIONS.USERS).findOne({
      _id: new ObjectId(userId)
    })

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Generate reset token
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET is not defined')
    }

    const resetToken = jwt.sign(
      { userId: targetUser._id.toString() },
      secret,
      { expiresIn: '1h' }
    )

    // Save token to user
    await db.collection(COLLECTIONS.USERS).updateOne(
      { _id: targetUser._id },
      {
        $set: {
          resetPasswordToken: resetToken,
          resetPasswordExpires: new Date(Date.now() + 3600000), // 1 hour
          updatedAt: new Date(),
          modifiedBy: {
            userId: adminUser._id.toString(),
            name: adminUser.name,
            timestamp: new Date().toISOString()
          }
        }
      }
    )

    // Send reset email
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`
    await sendEmail({
      to: email,
      subject: "Reset Your Password - Jimmy's Bali Ink",
      html: `
        <div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 24px;">
          <h1 style="color: #333; margin-bottom: 24px;">Reset Your Password</h1>
          
          <p style="color: #666; margin-bottom: 24px;">
            Hello ${targetUser.firstName},
          </p>
          
          <p style="color: #666; margin-bottom: 24px;">
            An administrator has requested a password reset for your account. Click the button below to set a new password:
          </p>
          
          <div style="text-align: center; margin: 32px 0;">
            <a href="${resetUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p style="color: #666; margin-bottom: 24px;">
            If the button doesn't work, you can also click on the link below or copy it to your browser:
          </p>
          
          <p style="color: #666; margin-bottom: 24px; word-break: break-all;">
            <a href="${resetUrl}" style="color: #007bff; text-decoration: none;">${resetUrl}</a>
          </p>
          
          <p style="color: #666; margin-bottom: 24px;">
            This link will expire in 1 hour for security reasons.
          </p>
          
          <p style="color: #666;">
            Best regards,<br>
            Jimmy's Bali Ink Team
          </p>
        </div>
      `
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Password reset email sent' 
    })
  } catch (error) {
    console.error('Error in admin-reset-password:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
