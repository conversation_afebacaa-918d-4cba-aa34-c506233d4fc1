interface User {
  userId: string
  email: string
  role: string
}

export function verifyJWT(token: string): User | null {
  try {
    // TODO: Implement proper JWT verification
    // For now, we'll just decode the token without verification
    const payload = JSON.parse(atob(token.split('.')[1]))
    return {
      userId: payload.sub,
      email: payload.email,
      role: payload.role
    }
  } catch (error) {
    console.error('Error verifying JWT:', error)
    return null
  }
}

export async function verifyAuth(): Promise<User | null> {
  // TODO: Implement proper auth verification
  // For now, we'll just return a mock admin user
  return {
    userId: '1',
    email: '<EMAIL>',
    role: 'ADMIN'
  }
}