"use client"

import { useEffect, useState } from 'react'
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  }
  category: {
    _id: string
    name: string
  }
  tags: {
    _id: string
    name: string
  }[]
  media: {
    url: string
    isPrimary: boolean
  }[]
  status: 'draft' | 'live'
  createdAt: string
}

interface ServiceDesignsProps {
  category: string
  tag?: string
}

// This map is kept for reference but currently not used directly
// It maps category names to their MongoDB IDs
// const categoryMap: Record<string, string> = {
//   'custom': '67ee16bb9cc63e740cae6c3f',
//   'flash': '67ee16db9cc63e740cae6c40',
//   'coverup': 'coverup'
// }

export function ServiceDesigns({ category, tag }: ServiceDesignsProps) {
  const [designs, setDesigns] = useState<Design[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDesigns = async () => {
      try {
        const params = new URLSearchParams()
        if (category) params.append('category', category)
        if (tag) params.append('tag', tag)

        const response = await fetch(`/api/designs?${params.toString()}`)
        if (!response.ok) throw new Error('Failed to fetch designs')
        const data = await response.json()

        // Log the raw data to debug
        console.log('Raw designs data:', data.map((d: any) => ({
          id: d._id,
          title: d.title,
          status: d.status,
          category: typeof d.category === 'object' ? d.category.name : d.category
        })))

        // Filter for live designs only
        const liveDesigns = data.filter((design: Design) => {
          // Check if design is live
          if (design.status !== 'live') return false;

          // Check if category matches
          if (!design.category) return false;

          // If category is an ID (24 character hex string), match directly with design.category._id
          if (category.match(/^[0-9a-fA-F]{24}$/)) {
            // Direct ID comparison
            if (typeof design.category === 'object' && design.category._id) {
              return design.category._id === category;
            } else if (typeof design.category === 'string') {
              return design.category === category;
            }
            return false;
          }

          // Otherwise, do a name-based comparison
          let designCategory = '';
          if (typeof design.category === 'object' && design.category.name) {
            designCategory = design.category.name.toLowerCase();
          } else if (typeof design.category === 'string') {
            designCategory = (design.category as string).toLowerCase();
          }

          // Compare with the requested category
          const requestedCategory = category.toLowerCase();
          const isMatch = designCategory.includes(requestedCategory) || requestedCategory.includes(designCategory);

          return isMatch;
        });

        console.log('Category:', category, 'Designs:', data.length, 'Filtered designs:', liveDesigns.length)
        setDesigns(liveDesigns)
      } catch (error) {
        console.error('Error fetching designs:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDesigns()
  }, [category, tag])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="aspect-square bg-muted animate-pulse rounded-lg" />
        ))}
      </div>
    )
  }

  if (designs.length === 0) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center h-48">
          <p className="text-muted-foreground">Designs coming soon...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="relative">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {designs.map((design) => (
              <CarouselItem key={design._id} className="pl-2 md:pl-4 basis-full sm:basis-1/2 md:basis-1/3">
                <Card className="group relative overflow-hidden">
                  <CardContent className="p-0">
                    <div className="aspect-square relative">
                      <Image
                        src={design.media.find(m => m.isPrimary)?.url || design.media[0].url}
                        alt={design.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-0 left-0 right-0 p-4 text-white space-y-2">
                        <p className="text-lg font-semibold">{design.title}</p>
                        <div className="flex flex-wrap gap-2">
                          {design.tags.map((tag) => (
                            <Badge
                              key={tag._id}
                              variant="outline"
                              className="text-white border-white/40 hover:border-white/60 bg-black/20"
                            >
                              {tag.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="absolute -left-12 top-1/2 -translate-y-1/2" />
          <CarouselNext className="absolute -right-12 top-1/2 -translate-y-1/2" />
        </Carousel>
      </div>

      <div className="flex justify-center">
        <Button asChild>
          <Link href="/designs">
            View All Designs
          </Link>
        </Button>
      </div>
    </div>
  )
}