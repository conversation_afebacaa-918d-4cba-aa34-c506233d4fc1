"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { toast } from 'sonner'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Pencil, Trash2, Loader2 } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AdminDesignCard } from '@/components/admin-design-card'

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  } | string
  category: {
    _id: string
    name: string
  } | string
  tags: {
    _id: string
    name: string
  }[]
  media: {
    url: string
    isPrimary: boolean
  }[]
  status: 'draft' | 'live'
  createdAt: string
}

interface Category {
  _id: string
  name: string
  description?: string
}

interface Size {
  _id: string
  name: string
  description?: string
}

interface Tag {
  _id: string
  name: string
  description?: string
}

export default function AdminDesigns() {
  const router = useRouter()
  const [designs, setDesigns] = useState<Design[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [sizes, setSizes] = useState<Size[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [loading, setLoading] = useState(true)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [designToDelete, setDesignToDelete] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')

  useEffect(() => {
    fetchDesigns()
    fetchCategories()
    fetchSizes()
    fetchTags()
  }, [])

  const fetchDesigns = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/designs', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch designs')
      }

      const data = await response.json()
      setDesigns(data)
    } catch (error) {
      console.error('Error fetching designs:', error)
      toast.error('Failed to fetch designs')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      console.log('Fetched categories:', data)
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to fetch categories')
    }
  }

  const fetchSizes = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/sizes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch sizes')
      }

      const data = await response.json()
      setSizes(data)
    } catch (error) {
      console.error('Error fetching sizes:', error)
      toast.error('Failed to fetch sizes')
    }
  }

  const fetchTags = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/tags', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch tags')
      }

      const data = await response.json()
      console.log('Fetched tags:', data)
      setTags(data)
    } catch (error) {
      console.error('Error fetching tags:', error)
      toast.error('Failed to fetch tags')
    }
  }

  const handleDelete = async () => {
    if (!designToDelete) return

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/designs?id=${designToDelete}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to delete design')
      }

      toast.success('Design deleted successfully')
      fetchDesigns()
    } catch (error) {
      console.error('Error deleting design:', error)
      toast.error('Failed to delete design')
    } finally {
      setDeleteDialogOpen(false)
      setDesignToDelete(null)
    }
  }

  const handleStatusChange = async (id: string, newStatus: 'draft' | 'live') => {
    try {
      const design = designs.find(d => d._id === id)
      if (!design) return

      const token = localStorage.getItem('token')
      const response = await fetch(`/api/designs?id=${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...design,
          status: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update design status')
      }

      toast.success('Design status updated successfully')
      fetchDesigns()
    } catch (error) {
      console.error('Error updating design status:', error)
      toast.error('Failed to update design status')
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-16 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  const getCategoryName = (category: Design['category']) => {
    console.log('Looking up category:', category)
    if (typeof category === 'object' && category !== null) {
      return category.name
    }
    const categoryDoc = categories.find(c => c._id === category)
    return categoryDoc?.name || 'Unknown Category'
  }

  const getSizeName = (sizeId: string) => {
    // If sizeId is already an object with name property, return that
    if (typeof sizeId === 'object' && sizeId !== null && 'name' in sizeId) {
      return sizeId.name
    }

    // Otherwise look up the size by ID
    const size = sizes.find(s => s._id === sizeId)
    return size?.name || 'Unknown Size'
  }

  const getTagName = (tagId: string) => {
    const tag = tags.find(t => t._id === tagId)
    return tag?.name || tagId
  }

  const renderDesignCard = (design: Design) => {
    // Transform the design object to match the expected format
    const formattedDesign = {
      ...design,
      tags: design.tags.map(tagId => {
        if (typeof tagId === 'string') {
          const tag = tags.find(t => t._id === tagId)
          return tag ? { _id: tag._id, name: tag.name } : null
        }
        return tagId
      }).filter(Boolean) as { _id: string; name: string }[]
    }

    return (
      <AdminDesignCard
        key={design._id}
        design={formattedDesign}
        onStatusChange={handleStatusChange}
        onDelete={(id) => {
          setDesignToDelete(id)
          setDeleteDialogOpen(true)
        }}
        getSizeName={getSizeName}
        getCategoryName={getCategoryName}
      />
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-8">
        <h1 className="text-2xl font-bold">Designs</h1>
        <Button className="w-full sm:w-auto" onClick={() => router.push('/admin/designs/new')}>
          <span className="whitespace-nowrap">Add New Design</span>
        </Button>
      </div>

      <Tabs defaultValue="all" className="space-y-4" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="live">Live</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {designs.map(renderDesignCard)}
          </div>
        </TabsContent>

        <TabsContent value="draft" className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {designs.filter(d => d.status === 'draft').map(renderDesignCard)}
          </div>
        </TabsContent>

        <TabsContent value="live" className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {designs.filter(d => d.status === 'live').map(renderDesignCard)}
          </div>
        </TabsContent>
      </Tabs>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the design.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}