import { NextResponse } from 'next/server'

export function GET() {
  return NextResponse.json(
    { error: 'Sizes API error occurred', message: 'This is a fallback response for sizes API route' },
    { status: 500 }
  )
}

export function POST() {
  return NextResponse.json(
    { error: 'Sizes API error occurred', message: 'This is a fallback response for sizes API route' },
    { status: 500 }
  )
}

export function PUT() {
  return NextResponse.json(
    { error: 'Sizes API error occurred', message: 'This is a fallback response for sizes API route' },
    { status: 500 }
  )
}

export function DELETE() {
  return NextResponse.json(
    { error: 'Sizes API error occurred', message: 'This is a fallback response for sizes API route' },
    { status: 500 }
  )
}
