"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { Mail, Phone, Calendar, Clock, User, Tag, Ruler, FileText, Image as ImageIcon, ArrowLeft, MapPin } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { getImageUrl } from "@/app/lib/file-utils"
import { ViewOnlyImageGrid } from "@/components/ViewOnlyImageGrid"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface Appointment {
  _id: string
  fullName: string
  email: string
  phone: string
  date: string
  time: string
  serviceType: {
    _id: string
    name: string
    slug: string
  }
  size: {
    _id: string
    name: string
    description: string
  }
  description?: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  media?: Array<{
    url: string
    previewUrl: string
  }>
  createdAt: string
}

export default function ViewAppointmentPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [appointment, setAppointment] = useState<Appointment | null>(null)
  const [loading, setLoading] = useState(true)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false)
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false)

  useEffect(() => {
    const fetchAppointment = async () => {
      try {
        const response = await fetch(`/api/admin/appointments/${params.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`
          }
        })

        if (!response.ok) {
          throw new Error("Failed to fetch appointment details")
        }

        const data = await response.json()
        setAppointment(data)
      } catch (error) {
        console.error("Error fetching appointment:", error)
        toast.error("Failed to load appointment details")
        router.push("/admin/appointments")
      } finally {
        setLoading(false)
      }
    }

    fetchAppointment()
  }, [params.id, router])

  const handleStatusUpdate = async (newStatus: Appointment["status"]) => {
    try {
      const response = await fetch(`/api/admin/appointments/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (!response.ok) throw new Error("Failed to update appointment status")

      const updatedAppointment = await response.json()
      setAppointment(prev => ({ ...prev!, status: newStatus }))
      toast.success("Appointment status updated successfully")
    } catch (error) {
      toast.error("Failed to update appointment status")
    }
  }

  if (loading || !appointment) {
    return (
      <div className="container mx-auto py-16 px-4">
        <div className="text-center">Loading appointment details...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Appointments
        </Button>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Appointment Details</h1>
            <p className="text-muted-foreground">
              Created on {format(new Date(appointment.createdAt), "PPP")}
            </p>
          </div>
          <Badge
            variant={
              appointment.status === "confirmed" ? "secondary" :
              appointment.status === "completed" ? "default" :
              appointment.status === "cancelled" ? "destructive" :
              "outline"
            }
            className="text-sm"
          >
            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
          </Badge>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Client Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <User className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Name</p>
                <p className="text-muted-foreground">{appointment.fullName}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Mail className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Email</p>
                <Link
                  href={`mailto:${appointment.email}`}
                  className="text-muted-foreground hover:text-primary"
                >
                  {appointment.email}
                </Link>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Phone className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Phone</p>
                <Link
                  href={`tel:${appointment.phone}`}
                  className="text-muted-foreground hover:text-primary"
                >
                  {appointment.phone}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Appointment Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Date</p>
                <p className="text-muted-foreground">
                  {format(new Date(appointment.date), "PPP")}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Time</p>
                <p className="text-muted-foreground">{appointment.time}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Tag className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Service</p>
                <p className="text-muted-foreground">{appointment.serviceType.name}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <MapPin className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Size</p>
                <div>
                  <p className="text-muted-foreground">{appointment.size.name}</p>
                  {appointment.size.description && (
                    <p className="text-sm text-muted-foreground mt-1">{appointment.size.description}</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {appointment.description && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground whitespace-pre-wrap">{appointment.description}</p>
            </CardContent>
          </Card>
        )}

        {appointment.media && appointment.media.length > 0 && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Reference Images</CardTitle>
              <CardDescription>
                Client provided {appointment.media.length} reference {appointment.media.length === 1 ? 'image' : 'images'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ViewOnlyImageGrid images={appointment.media} showFullSizeButton={true} disableZoomEffect={false} />
            </CardContent>
          </Card>
        )}

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Actions</CardTitle>
            <CardDescription>
              Manage this appointment
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-4">
            <Button
              variant="outline"
              onClick={() => router.push(`/admin/appointments/${params.id}/edit`)}
            >
              Edit Appointment
            </Button>

            {appointment.status === "pending" && (
              <>
                <Button
                  onClick={() => setConfirmDialogOpen(true)}
                >
                  Confirm Appointment
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setCancelDialogOpen(true)}
                >
                  Cancel Appointment
                </Button>
              </>
            )}

            {appointment.status === "confirmed" && (
              <Button
                onClick={() => setCompleteDialogOpen(true)}
              >
                Mark as Completed
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Status update dialogs */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to confirm this appointment? An email will be sent to notify the client.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setConfirmDialogOpen(false);
                handleStatusUpdate("confirmed");
              }}
              className="bg-green-500 hover:bg-green-600"
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel this appointment? An email will be sent to notify the client.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>No, keep it</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setCancelDialogOpen(false);
                handleStatusUpdate("cancelled");
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              Yes, cancel it
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={completeDialogOpen} onOpenChange={setCompleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Complete Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to mark this appointment as completed? An email will be sent to thank the client.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setCompleteDialogOpen(false);
                handleStatusUpdate("completed");
              }}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Complete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}