import { useState } from "react"
import Image from "next/image"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Pencil, Trash, ImageOff } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getImageUrl } from "@/app/lib/file-utils"

interface Tag {
  _id: string
  name: string
}

interface AdminGalleryCardProps {
  _id: string
  imageUrl: string
  category?: string
  tags: Tag[]
  isFeatured: boolean
  updatingFeatured: boolean
  onToggleFeatured: () => void
  onEdit: () => void
  onDelete: () => void
}

export function AdminGalleryCard({
  _id,
  imageUrl,
  category,
  tags,
  isFeatured,
  onEdit,
  onDelete
}: AdminGalleryCardProps) {
  const [imageError, setImageError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Use our image utility function to get the correct URL with options
  const processedImageUrl = getImageUrl(imageUrl, {
    cacheBust: retryCount > 0, // Add cache busting if we've retried
    bypassCache: retryCount > 1 // Bypass cache on second retry
  })

  // Handle image loading error
  const handleImageError = () => {
    console.error(`Error loading admin gallery image: ${imageUrl}`);

    // Only retry up to 2 times
    if (retryCount < 2) {
      setRetryCount(retryCount + 1);
    } else {
      setImageError(true);
    }
  };

  return (
    <Card className="group relative overflow-hidden bg-black rounded-lg">
      <div className="relative aspect-square">
        {!imageError ? (
          <Image
            src={processedImageUrl}
            alt={category || 'Gallery image'}
            fill
            className="object-cover"
            unoptimized={true} // Always use unoptimized for MinIO images
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-800">
            <div className="text-center text-gray-400">
              <ImageOff className="h-10 w-10 mx-auto mb-2" />
              <span>Image unavailable</span>
            </div>
          </div>
        )}
        {isFeatured && (
          <div className="absolute top-2 right-2 z-10 bg-black/80 text-white px-2 py-0.5 rounded-lg text-xs font-medium">
            Featured
          </div>
        )}

        {/* Dark overlay with category and tags */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2">
            <div className="space-y-1">
              <p className="text-lg font-semibold capitalize text-white">
                {category || 'Uncategorized'}
              </p>
            </div>

            {/* Tags */}
            {tags && tags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge
                    key={tag._id}
                    variant="outline"
                    className="text-xs px-2.5 py-1 bg-black/60 text-white border-white/20 hover:bg-black/80"
                  >
                    {tag.name}
                  </Badge>
                ))}
              </div>
            ) : (
              <Badge
                variant="outline"
                className="text-xs px-2.5 py-1 bg-black/60 text-white border-white/20"
              >
                No Tags
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Controls section */}
      <div className="bg-black p-4">
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onEdit}
            className="h-8 bg-zinc-800 hover:bg-zinc-700 text-white border-none"
          >
            <Pencil className="h-4 w-4 mr-1" />
            Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onDelete}
            className="h-8"
          >
            <Trash className="h-4 w-4 mr-1" />
            Delete
          </Button>
        </div>
      </div>
    </Card>
  )
}