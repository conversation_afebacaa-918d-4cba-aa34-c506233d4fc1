"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { PlusCircle, Pencil, Trash2 } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"

interface Promotion {
  _id: string
  title: string
  description: string
  price: string
  startDate: string
  endDate: string
  ctaText: string
  ctaLink: string
  isActive: boolean
}

export default function PromotionsPage() {
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPromotions()
  }, [])

  async function fetchPromotions() {
    try {
      const response = await fetch('/api/promotions')
      const data = await response.json()
      setPromotions(Array.isArray(data) ? data : [])
    } catch (error) {
      console.error('Error fetching promotions:', error)
      toast({
        title: "Error",
        description: "Failed to fetch promotions",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  async function deletePromotion(id: string) {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/promotions/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete promotion')
      }

      setPromotions(promotions.filter(promo => promo._id !== id))
      toast({
        title: "Success",
        description: "Promotion deleted successfully",
      })
    } catch (error) {
      console.error('Error deleting promotion:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete promotion",
        variant: "destructive",
      })
    }
  }

  function isPromotionActive(promotion: Promotion) {
    if (!promotion.isActive) return false

    const now = new Date()
    const startDate = new Date(promotion.startDate)
    const endDate = new Date(promotion.endDate)

    return now >= startDate && now <= endDate
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4">
        <h2 className="text-3xl font-bold tracking-tight">Promotions</h2>
        <Link href="/admin/promotions/new" className="w-full sm:w-auto">
          <Button className="w-full sm:w-auto">
            <PlusCircle className="mr-2 h-4 w-4" />
            <span className="whitespace-nowrap">New Promotion</span>
          </Button>
        </Link>
      </div>

      <div className="rounded-md border">
        {loading ? (
          <div className="flex justify-center items-center h-24">
            <p>Loading promotions...</p>
          </div>
        ) : promotions.length > 0 ? (
          <div className="relative w-full overflow-auto">
            <table className="w-full caption-bottom text-sm">
              <thead className="[&_tr]:border-b">
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium">Title</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Price</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Start Date</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">End Date</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                </tr>
              </thead>
              <tbody className="[&_tr:last-child]:border-0">
                {promotions.map((promotion) => (
                  <tr
                    key={promotion._id}
                    className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                  >
                    <td className="p-4 align-middle">
                      <div className="font-medium">{promotion.title}</div>
                      <div className="text-sm text-muted-foreground">{promotion.description.substring(0, 60)}{promotion.description.length > 60 ? '...' : ''}</div>
                    </td>
                    <td className="p-4 align-middle">{promotion.price}</td>
                    <td className="p-4 align-middle">{format(new Date(promotion.startDate), 'MMM d, yyyy')}</td>
                    <td className="p-4 align-middle">{format(new Date(promotion.endDate), 'MMM d, yyyy')}</td>
                    <td className="p-4 align-middle">
                      <Badge variant={isPromotionActive(promotion) ? "default" : "secondary"}>
                        {isPromotionActive(promotion) ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="flex space-x-2">
                        <Link href={`/admin/promotions/${promotion._id}/edit`}>
                          <Button variant="outline" size="sm">
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deletePromotion(promotion._id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="flex justify-center items-center h-24">
            <p>No promotions found</p>
          </div>
        )}
      </div>
    </div>
  )
}