import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { connectToDatabase } from '@/lib/mongodb'
import { signJWT } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Registration attempt with data:', {
      ...body,
      password: body.password ? '[REDACTED]' : undefined
    })

    const { firstName, surname, email, password, dateOfBirth } = body

    // Validate input
    if (!firstName?.trim() || !surname?.trim() || !email?.trim() || !password || !dateOfBirth) {
      console.log('Missing or invalid required fields:', { 
        firstName: !!firstName?.trim(), 
        surname: !!surname?.trim(), 
        email: !!email?.trim(), 
        password: !!password, 
        dateOfBirth: !!dateOfBirth 
      })
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      console.log('Invalid email format:', email)
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      )
    }

    const normalizedEmail = email.toLowerCase().trim()
    const name = `${firstName.trim()} ${surname.trim()}`

    const db = await connectToDatabase()
    const users = db.collection('users')

    // Check if email already exists
    const existingUser = await users.findOne({ email: normalizedEmail })
    console.log('Existing user check:', existingUser ? 'User found' : 'No user found')
    
    if (existingUser) {
      console.log('Email already registered:', normalizedEmail)
      return NextResponse.json(
        { error: "Email already registered" },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { error: "Password must be at least 8 characters long" },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Check if this is the first account (will be admin)
    const totalAccounts = await users.countDocuments()
    console.log('Total accounts in database:', totalAccounts)

    // Create user document
    const newUser = {
      email: normalizedEmail,
      password: hashedPassword,
      firstName: firstName.trim(),
      surname: surname.trim(),
      dateOfBirth: new Date(dateOfBirth),
      role: totalAccounts === 0 ? 'admin' : 'user',
      createdAt: new Date(),
      lastLogin: new Date()
    }

    const result = await users.insertOne(newUser)
    console.log('Account created successfully:', result.insertedId)

    // Create JWT token
    const token = await signJWT({
      userId: result.insertedId.toString(),
      email: normalizedEmail,
      firstName: firstName.trim(),
      surname: surname.trim(),
      role: newUser.role
    })

    // Return success with token and user data
    const { password: _, ...userWithoutPassword } = newUser
    return NextResponse.json({
      message: "Account created successfully",
      token,
      user: {
        ...userWithoutPassword,
        id: result.insertedId
      }
    })
  } catch (error) {
    console.error('Detailed registration error:', error)
    
    // Handle MongoDB duplicate key error
    if ((error as any).code === 11000) {
      return NextResponse.json(
        { error: "Email already registered" },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to create account. Please try again." },
      { status: 500 }
    )
  }
} 