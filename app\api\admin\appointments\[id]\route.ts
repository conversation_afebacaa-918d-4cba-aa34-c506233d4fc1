import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { sendEmail } from '@/app/lib/email'
import { STUDIO_INFO } from '@/lib/config'
import { format } from 'date-fns'
import { ObjectId } from 'mongodb'
import { createEmailAttachment } from '@/app/lib/file-utils'

interface Appointment {
  _id: string
  fullName: string
  email: string
  phone: string
  date: string
  time: string
  serviceType: {
    _id: string
    name: string
    slug: string
  }
  size: {
    _id: string
    name: string
    description: string
  }
  description?: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  media?: Array<{
    url: string
    previewUrl: string
  }>
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get token from authorization header or cookie
    const token = request.headers.get('authorization')?.split(' ')[1] ||
                 request.cookies.get('token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      )
    }

    const isAuthenticated = await verifyAuth(token)
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    const client = await connectToDatabase()
    const appointment = await client
      .collection('appointments')
      .findOne({ _id: new ObjectId(params.id) })

    if (!appointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      )
    }

    // Ensure date is in ISO format for consistency
    if (appointment.date) {
      const appointmentDate = new Date(appointment.date)
      appointment.date = appointmentDate.toISOString()
      console.log('Appointment date for edit:', appointment.date)
    }

    // Convert ObjectId to string
    appointment._id = appointment._id.toString()

    return NextResponse.json(appointment)
  } catch (error) {
    console.error('Error fetching appointment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const user = await verifyAuth()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const data = await request.json()
    const db = await connectToDatabase()

    // Get current appointment data
    const currentAppointment = await db.collection('appointments').findOne({
      _id: new ObjectId(params.id)
    })

    if (!currentAppointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      )
    }

    // Type the current appointment
    const typedCurrentAppointment = {
      _id: currentAppointment._id.toString(),
      fullName: currentAppointment.fullName as string,
      email: currentAppointment.email as string,
      phone: currentAppointment.phone as string,
      date: currentAppointment.date as string,
      time: currentAppointment.time as string,
      serviceType: currentAppointment.serviceType as {
        _id: string,
        name: string,
        slug: string
      },
      size: currentAppointment.size as {
        _id: string,
        name: string,
        description: string
      },
      description: currentAppointment.description as string | undefined,
      status: currentAppointment.status as 'pending' | 'confirmed' | 'completed' | 'cancelled',
      media: currentAppointment.media as Array<{
        url: string,
        previewUrl: string
      }> | undefined
    } satisfies Appointment;

    // Merge current data with updates, excluding _id
    let processedData = { ...data }

    // Handle date timezone issues if date is provided
    if (data.date) {
      // Parse the date string from the request
      const dateString = data.date
      console.log('Original date string from update:', dateString)

      // Extract the date components from the ISO string
      // The format is YYYY-MM-DDTHH:mm:ss.sssZ
      const dateParts = dateString.split('T')[0].split('-')
      const year = parseInt(dateParts[0])
      const month = parseInt(dateParts[1]) - 1 // Months are 0-indexed in JavaScript
      const day = parseInt(dateParts[2])

      console.log(`Extracted date parts (update): year=${year}, month=${month}, day=${day}`)

      // Create a date object with the extracted parts
      // Add 1 to the day to compensate for timezone difference
      // This is a specific fix for the timezone issue between client and server
      const adjustedDay = day + 1
      console.log(`Adjusted day (update): ${adjustedDay} (original: ${day})`)

      const appointmentDate = new Date(Date.UTC(year, month, adjustedDay))
      console.log('Final update appointment date (UTC):', appointmentDate.toISOString())

      processedData.date = appointmentDate
    }

    // Create an object with all the data we want to update
    const updatedAppointmentData = {
      ...typedCurrentAppointment,
      ...processedData,
      updatedAt: new Date()
    }

    // Remove the _id field from the update data
    const { _id, ...updateData } = updatedAppointmentData

    // Update the appointment
    const result = await db.collection('appointments').findOneAndUpdate(
      { _id: new ObjectId(params.id) },
      { $set: updateData },
      { returnDocument: 'after' }
    )

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to update appointment' },
        { status: 500 }
      )
    }

    const response = NextResponse.json({
      message: 'Appointment updated successfully',
      appointment: {
        ...result,
        _id: result._id.toString()
      }
    })

    // Handle email notifications
    if (data.status && data.status !== typedCurrentAppointment.status) {
      try {
        // Get the date from the update data
        const appointmentDate = new Date(updateData.date)

        // Format for display in email - use the date directly since we've already
        // ensured it's correctly formatted when we processed the date above
        const formattedDate = format(appointmentDate, 'EEEE, MMMM d, yyyy')

        console.log('Email formatted date:', formattedDate)

        let emailSubject = ""
        let emailContent = ""

        switch (data.status) {
          case "confirmed":
            emailSubject = "Your Appointment Has Been Confirmed"
            emailContent = `
              <div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 24px;">
                <h1 style="color: #333; margin-bottom: 24px;">Appointment Confirmed!</h1>

                <p style="color: #666; margin-bottom: 24px;">
                  Dear ${updateData.fullName},
                </p>

                <p style="color: #666; margin-bottom: 24px;">
                  Your appointment has been confirmed for ${formattedDate} at ${updateData.time}.
                </p>

                <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
                  <h3 style="color: #444; margin-bottom: 16px;">Appointment Details</h3>
                  <div style="color: #666;">
                    <p><strong>Service:</strong> ${updateData.serviceType.name}</p>
                    <p><strong>Size:</strong> ${updateData.size.name}</p>
                    <p><strong>Date:</strong> ${formattedDate}</p>
                    <p><strong>Time:</strong> ${updateData.time}</p>
                    ${updateData.description ? `<p><strong>Description:</strong> ${updateData.description}</p>` : ''}
                    <p><strong>Status:</strong> <span style="color: #4CAF50; font-weight: bold;">Confirmed</span></p>
                  </div>
                </div>

                <p style="color: #666; margin-bottom: 24px;">
                  Please arrive 10-15 minutes before your scheduled time. If you need to reschedule or cancel, please contact us at least 24 hours in advance.
                </p>

                <p style="color: #666; margin-bottom: 24px;">
                  We look forward to seeing you!
                </p>

                <p style="color: #666;">
                  Best regards,<br>
                  Jimmy's Bali Ink<br>
                  <a href="https://jimmysbaliink.com" style="color: #007bff; text-decoration: none;">jimmysbaliink.com</a>
                </p>
              </div>
            `
            break

          case "cancelled":
            emailSubject = "Your Appointment Has Been Cancelled"
            emailContent = `
              <div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 24px;">
                <h1 style="color: #333; margin-bottom: 24px;">Appointment Cancelled</h1>

                <p style="color: #666; margin-bottom: 24px;">
                  Dear ${updateData.fullName},
                </p>

                <p style="color: #666; margin-bottom: 24px;">
                  Your appointment scheduled for ${formattedDate} at ${updateData.time} has been cancelled.
                </p>

                <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
                  <h3 style="color: #444; margin-bottom: 16px;">Appointment Details</h3>
                  <div style="color: #666;">
                    <p><strong>Service:</strong> ${updateData.serviceType.name}</p>
                    <p><strong>Size:</strong> ${updateData.size.name}</p>
                    <p><strong>Date:</strong> ${formattedDate}</p>
                    <p><strong>Time:</strong> ${updateData.time}</p>
                    ${updateData.description ? `<p><strong>Description:</strong> ${updateData.description}</p>` : ''}
                    <p><strong>Status:</strong> <span style="color: #F44336; font-weight: bold;">Cancelled</span></p>
                  </div>
                </div>

                <p style="color: #666; margin-bottom: 24px;">
                  If you would like to reschedule, please visit our website to book a new appointment.
                </p>

                <div style="text-align: center; margin: 32px 0;">
                  <a href="https://jimmysbaliink.com/book" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">Book New Appointment</a>
                </div>

                <p style="color: #666;">
                  Best regards,<br>
                  Jimmy's Bali Ink<br>
                  <a href="https://jimmysbaliink.com" style="color: #007bff; text-decoration: none;">jimmysbaliink.com</a>
                </p>
              </div>
            `
            break

          case "completed":
            emailSubject = "Thank You for Your Visit"
            emailContent = `
              <div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 24px;">
                <h1 style="color: #333; margin-bottom: 24px;">Thank You for Your Visit!</h1>

                <p style="color: #666; margin-bottom: 24px;">
                  Dear ${updateData.fullName},
                </p>

                <p style="color: #666; margin-bottom: 24px;">
                  Thank you for choosing Jimmy's Bali Ink. We hope you love your new tattoo!
                </p>

                <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
                  <h3 style="color: #444; margin-bottom: 16px;">Appointment Details</h3>
                  <div style="color: #666;">
                    <p><strong>Service:</strong> ${updateData.serviceType.name}</p>
                    <p><strong>Size:</strong> ${updateData.size.name}</p>
                    <p><strong>Date:</strong> ${formattedDate}</p>
                    <p><strong>Time:</strong> ${updateData.time}</p>
                    ${updateData.description ? `<p><strong>Description:</strong> ${updateData.description}</p>` : ''}
                    <p><strong>Status:</strong> <span style="color: #2196F3; font-weight: bold;">Completed</span></p>
                  </div>
                </div>

                <p style="color: #666; margin-bottom: 24px;">
                  If you have a moment, we'd love to hear about your experience. Feel free to leave us a review or share your tattoo on social media and tag us.
                </p>

                <div style="text-align: center; margin: 32px 0;">
                  <a href="${process.env.NEXT_PUBLIC_APP_URL}/aftercare" style="background-color: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">Aftercare Instructions</a>
                </div>

                <p style="color: #666; margin-bottom: 24px;">
                  Remember to follow our aftercare instructions for the best healing results.
                </p>

                <p style="color: #666;">
                  Best regards,<br>
                  Jimmy's Bali Ink<br>
                  <a href="https://jimmysbaliink.com" style="color: #007bff; text-decoration: none;">jimmysbaliink.com</a>
                </p>
              </div>
            `
            break
        }

        if (emailSubject && emailContent) {
          // Prepare attachments if there are reference images
          const attachments = updateData.media?.map((image: { url: string }, index: number) =>
            createEmailAttachment(image.url, index, 'reference')
          ) || [];

          // Add reference images section to email content if there are images
          if (attachments.length > 0) {
            emailContent = emailContent.replace('</div>', `
              <div style="margin-top: 24px;">
                <h3 style="color: #444; margin-bottom: 16px;">Reference Images</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                  ${attachments.map((attachment: { cid: string }) => `
                    <div style="position: relative;">
                      <img
                        src="cid:${attachment.cid}"
                        alt="Reference Image"
                        style="width: 100%; border-radius: 8px; border: 1px solid #eee;"
                      />
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>
            `);
          }

          // Send email and wait for it to complete
          await sendEmail({
            to: updateData.email,
            subject: emailSubject,
            html: emailContent.trim(),
            attachments
          });

          console.log('Status update email sent successfully:', {
            appointmentId: params.id,
            status: data.status,
            recipient: updateData.email
          });
        }
      } catch (error) {
        console.error('Error sending status update email:', {
          error,
          appointmentId: params.id,
          status: data.status
        });
        // Don't throw the error, just log it and continue
        // The appointment update was successful, we don't want to fail it just because the email failed
      }
    }

    return response
  } catch (error) {
    console.error('Error updating appointment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}