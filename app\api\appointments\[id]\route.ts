import { NextRequest, NextResponse } from 'next/server'
import clientPromise from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    if (!id || !ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid appointment ID' },
        { status: 400 }
      )
    }

    const client = await clientPromise
    const db = client.db()

    const appointment = await db.collection('appointments').findOne({
      _id: new ObjectId(id)
    })

    if (!appointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      )
    }

    // Convert ObjectId to string for JSON serialization
    const formattedAppointment = {
      ...appointment,
      _id: appointment._id.toString(),
      serviceType: {
        ...appointment.serviceType,
        _id: appointment.serviceType._id.toString()
      },
      size: {
        ...appointment.size,
        _id: appointment.size._id.toString()
      }
    }

    return NextResponse.json(formattedAppointment)
  } catch (error) {
    console.error('Error fetching appointment:', error)
    return NextResponse.json(
      { error: 'Failed to fetch appointment' },
      { status: 500 }
    )
  }
}
