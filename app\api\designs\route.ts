import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { ObjectId } from 'mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

interface TagDetail {
  _id: string
  name: string
}

interface CategoryDetail {
  _id: string
  name: string
}

interface TagDocument {
  _id: ObjectId
  name: string
}

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    // Public access for designs - no authentication required
    console.log('Designs API called with URL:', request.url)

    const db = await getDb()
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')

    console.log('Search params:', Object.fromEntries(searchParams.entries()))

    const query: any = {}
    if (category) {
      // Try to find the category by name (case insensitive)
      const categoryDoc = await db.collection(COLLECTIONS.CATEGORIES).findOne({
        name: { $regex: new RegExp(category, 'i') }
      })

      if (categoryDoc) {
        console.log(`Found category for '${category}':`, categoryDoc)
        query.category = categoryDoc._id.toString()
      } else {
        console.log(`No category found for '${category}'`)
      }
    }

    const designs = await db.collection(COLLECTIONS.DESIGNS).find(query).toArray()
    console.log('Fetched designs:', designs.map(d => ({ _id: d._id, category: d.category })))

    // Fetch categories for all designs
    const categoryIds = designs.map(design => {
      try {
        // Check if category is already an ObjectId
        if (design.category instanceof ObjectId) {
          return design.category;
        }
        // Check if category is a string that can be converted to ObjectId
        if (typeof design.category === 'string' && design.category.match(/^[0-9a-fA-F]{24}$/)) {
          return new ObjectId(design.category);
        }
        // If category is an object with _id property
        if (design.category && typeof design.category === 'object' && '_id' in design.category) {
          const categoryId = design.category._id;
          if (categoryId instanceof ObjectId) {
            return categoryId;
          }
          if (typeof categoryId === 'string' && categoryId.match(/^[0-9a-fA-F]{24}$/)) {
            return new ObjectId(categoryId);
          }
        }
        console.error('Invalid category format:', design.category);
        return null;
      } catch (e) {
        console.error('Invalid category ID:', design.category, e);
        return null;
      }
    }).filter((id): id is ObjectId => id !== null)
    console.log('Extracted category IDs:', categoryIds)

    const categories = await db.collection(COLLECTIONS.CATEGORIES)
      .find({ _id: { $in: categoryIds } })
      .toArray()
    console.log('Found categories:', categories)

    const categoryMap = new Map(categories.map(cat => [cat._id.toString(), cat]))
    console.log('Category map keys:', Array.from(categoryMap.keys()))

    // Fetch sizes for all designs
    const sizeIds = designs.map(design => {
      try {
        // Check if size is already an ObjectId
        if (design.size instanceof ObjectId) {
          return design.size;
        }
        // Check if size is a string that can be converted to ObjectId
        if (typeof design.size === 'string' && design.size.match(/^[0-9a-fA-F]{24}$/)) {
          return new ObjectId(design.size);
        }
        // If size is an object with _id property
        if (design.size && typeof design.size === 'object' && '_id' in design.size) {
          const sizeId = design.size._id;
          if (sizeId instanceof ObjectId) {
            return sizeId;
          }
          if (typeof sizeId === 'string' && sizeId.match(/^[0-9a-fA-F]{24}$/)) {
            return new ObjectId(sizeId);
          }
        }
        // If we get here, the size is invalid or missing
        if (design.size) {
          console.error('Invalid size format:', design.size);
        }
        return null;
      } catch (e) {
        console.error('Invalid size ID:', design.size, e);
        return null;
      }
    }).filter((id): id is ObjectId => id !== null)
    console.log('Extracted size IDs:', sizeIds)

    const sizes = await db.collection(COLLECTIONS.SIZES)
      .find({ _id: { $in: sizeIds } })
      .toArray()
    console.log('Found sizes:', sizes)

    const sizeMap = new Map(sizes.map(size => [size._id.toString(), size]))
    console.log('Size map keys:', Array.from(sizeMap.keys()))

    // Fetch tags for all designs
    const tagIds = designs.flatMap(design =>
      (design.tags || []).map((tagItem: any) => {
        try {
          // Check if tag is already an ObjectId
          if (tagItem instanceof ObjectId) {
            return tagItem;
          }
          // Check if tag is a string that can be converted to ObjectId
          if (typeof tagItem === 'string' && tagItem.match(/^[0-9a-fA-F]{24}$/)) {
            return new ObjectId(tagItem);
          }
          // If tag is an object with _id property
          if (tagItem && typeof tagItem === 'object' && '_id' in tagItem) {
            const tagId = tagItem._id;
            if (tagId instanceof ObjectId) {
              return tagId;
            }
            if (typeof tagId === 'string' && tagId.match(/^[0-9a-fA-F]{24}$/)) {
              return new ObjectId(tagId);
            }
          }
          // If we get here, the tag is invalid
          console.error('Invalid tag format:', tagItem);
          return null;
        } catch (e) {
          console.error('Invalid tag ID:', tagItem, e);
          return null;
        }
      })
    ).filter((id): id is ObjectId => id !== null)

    const tags = await db.collection(COLLECTIONS.TAGS)
      .find({ _id: { $in: tagIds } })
      .toArray() as TagDocument[]

    const tagMap = new Map(tags.map((tag: TagDocument) => [tag._id.toString(), tag]))

    // Format the response
    const formattedDesigns = designs.map(design => {
      const categoryId = design.category?.toString()
      const categoryDoc = categoryId ? categoryMap.get(categoryId) : null

      // Format tags
      const formattedTags = (design.tags || [])
        .map((tagId: string) => {
          const tag = tagMap.get(tagId.toString()) as TagDocument | undefined
          return tag ? {
            _id: tag._id.toString(),
            name: tag.name
          } : null
        })
        .filter((tag: { _id: string; name: string } | null): tag is TagDetail => tag !== null)

      let sizeId = design.size?.toString()
      let sizeDoc = null

      // Check if size is already an object with _id and name
      if (design.size && typeof design.size === 'object' && '_id' in design.size && 'name' in design.size) {
        sizeDoc = design.size
      } else if (sizeId) {
        // Otherwise look up the size by ID
        sizeDoc = sizeMap.get(sizeId)
      }

      return {
        ...design,
        _id: design._id.toString(),
        category: categoryDoc ? {
          _id: categoryDoc._id.toString(),
          name: categoryDoc.name
        } : {
          _id: design.category || '',
          name: 'Unknown Category'
        },
        size: sizeDoc ? {
          _id: typeof sizeDoc._id === 'string' ? sizeDoc._id : sizeDoc._id.toString(),
          name: sizeDoc.name
        } : {
          _id: design.size || '',
          name: 'Unknown Size'
        },
        tags: formattedTags
      }
    })

    return NextResponse.json(formattedDesigns)
  } catch (error) {
    console.error('Error fetching designs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch designs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, price, size, category, tags, media } = body

    if (!title || !price || !size || !category || !media || media.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const db = await getDb()

    // Validate category exists
    const categoryExists = await db.collection(COLLECTIONS.CATEGORIES).findOne({ _id: new ObjectId(category) })
    if (!categoryExists) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    // Validate size exists
    const sizeExists = await db.collection(COLLECTIONS.SIZES).findOne({ _id: new ObjectId(size) })
    if (!sizeExists) {
      return NextResponse.json(
        { error: 'Invalid size' },
        { status: 400 }
      )
    }

    const result = await db.collection(COLLECTIONS.DESIGNS).insertOne({
      title,
      description,
      price: parseFloat(price),
      size: size,
      category: category,
      tags: tags || [],
      media,
      status: body.status || 'draft',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: {
        userId: authResult.userId,
        role: authResult.role,
        timestamp: new Date().toISOString()
      }
    })

    return NextResponse.json({
      message: 'Design created successfully',
      id: result.insertedId
    })
  } catch (error) {
    console.error('Error creating design:', error)
    return NextResponse.json(
      { error: 'Failed to create design' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const isAuthorized = await verifyAuth(token)

    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Design ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { title, description, price, size, category, tags, media, status } = body

    if (!title || !price || !size || !category || !media || media.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const db = await getDb()
    const result = await db.collection(COLLECTIONS.DESIGNS).updateOne(
      { _id: new ObjectId(id) },
      {
        $set: {
          title,
          description,
          price: parseFloat(price),
          size,
          category,
          tags: tags || [],
          media,
          status,
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Design not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Design updated successfully'
    })
  } catch (error) {
    console.error('Error updating design:', error)
    return NextResponse.json(
      { error: 'Failed to update design' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const isAuthorized = await verifyAuth(token)

    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Design ID is required' },
        { status: 400 }
      )
    }

    const db = await getDb()
    const result = await db.collection(COLLECTIONS.DESIGNS).deleteOne({
      _id: new ObjectId(id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Design not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Design deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting design:', error)
    return NextResponse.json(
      { error: 'Failed to delete design' },
      { status: 500 }
    )
  }
}