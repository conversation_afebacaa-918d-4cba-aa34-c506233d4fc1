"use client"

import { useState, useEffect } from "react"
import { Size } from "@/types"
import { ItemManager } from "../shared/ItemManager"
import { ItemDialog } from "../shared/ItemDialog"
import { DeleteConfirmDialog } from "../shared/DeleteConfirmDialog"
import { toast } from "sonner"

export default function SizesPage() {
  const [sizes, setSizes] = useState<Size[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [editingSize, setEditingSize] = useState<Size | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)

  useEffect(() => {
    fetchSizes()
  }, [])

  const fetchSizes = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/sizes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (!response.ok) throw new Error('Failed to fetch sizes')
      const data = await response.json()
      setSizes(data)
    } catch (error) {
      console.error('Error fetching sizes:', error)
      toast.error('Failed to fetch sizes')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (size: Size) => {
    setEditingSize(size)
    setDialogOpen(true)
  }

  const handleDelete = (id: string) => {
    setDeletingId(id)
    setDeleteDialogOpen(true)
  }

  const handleSubmit = async (formData: { name: string; description?: string }) => {
    try {
      setSubmitting(true)
      const token = localStorage.getItem('token')
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      if (editingSize) {
        const response = await fetch(`/api/sizes?id=${editingSize._id}`, {
          method: 'PUT',
          headers,
          body: JSON.stringify({
            ...formData,
            order: editingSize.order
          })
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to update size')
        }

        await fetchSizes()
        toast.success('Size updated successfully')
      } else {
        // Get the highest order number from existing sizes
        const highestOrder = sizes.reduce((max, size) =>
          size.order && size.order > max ? size.order : max, 0)

        const response = await fetch('/api/sizes', {
          method: 'POST',
          headers,
          body: JSON.stringify({
            ...formData,
            order: highestOrder + 1
          })
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to create size')
        }

        const newSize = await response.json()
        // Add the new size to the list and sort by order
        setSizes(prev => [...prev, newSize].sort((a, b) => (a.order || 0) - (b.order || 0)))
        toast.success('Size created successfully')
      }

      setDialogOpen(false)
      setEditingSize(null)
    } catch (error) {
      console.error('Error saving size:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save size')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!deletingId) return

    try {
      const token = localStorage.getItem('token')
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      const response = await fetch(`/api/sizes?id=${deletingId}`, {
        method: 'DELETE',
        headers
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete size')
      }

      setSizes(sizes.filter(size => size._id !== deletingId))
      setDeleteDialogOpen(false)
      setDeletingId(null)
      toast.success('Size deleted successfully')
    } catch (error) {
      console.error('Error deleting size:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete size')
    }
  }

  const handleReorder = async (reorderedSizes: Size[]) => {
    try {
      const response = await fetch('/api/sizes/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sizes: reorderedSizes })
      })
      if (!response.ok) throw new Error('Failed to update size order')
      setSizes(reorderedSizes)
    } catch (error) {
      console.error('Error reordering sizes:', error)
      // Revert to original order if failed
      fetchSizes()
    }
  }

  return (
    <div className="container mx-auto py-6">
      <ItemManager
        title="Sizes"
        description="Manage your available sizes"
        items={sizes}
        loading={loading}
        onAddClick={() => {
          setEditingSize(null);
          setDialogOpen(true);
        }}
        onEditClick={handleEdit}
        onDeleteClick={handleDelete}
        onReorder={handleReorder}
        sortable={true}
      />

      <ItemDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSubmit={handleSubmit}
        title={editingSize ? "Edit Size" : "Add Size"}
        submitLabel={editingSize ? "Save" : "Add"}
        initialData={editingSize || undefined}
      />

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Size"
        description="Are you sure you want to delete this size? This action cannot be undone."
      />
    </div>
  )
}