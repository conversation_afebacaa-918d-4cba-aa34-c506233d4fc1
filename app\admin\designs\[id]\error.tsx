'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

export default function AdminDesignDetailError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Admin design detail error:', error)
  }, [error])

  return (
    <div className="container py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-destructive">Design Detail Error</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            An error occurred while loading the design details. This could be due to a network issue or a problem with the server.
          </p>
          <div className="flex flex-col gap-2">
            <Button 
              onClick={reset}
              variant="default"
            >
              Try again
            </Button>
            <Button 
              variant="outline"
              asChild
            >
              <Link href="/admin/designs">
                Return to Designs
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
