version: '3.9'

services:
  nextjs-app:
    image: 192.168.0.59:6500/jimmys-bali-ink:latest
    container_name: jimmys-bali-ink
    restart: unless-stopped
    ports:
      - "3050:3000"
    environment:
      # Application Environment
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_URL=https://jimmysbaliink.com
      - JWT_SECRET=8f45d7a1c3b2e6f890d4p2m5n8q7r3t6v9x2z5b8h4k7m1n4p6s9w2y5a8d1g4j7

      # Database Configuration
      - MONGODB_URI=*****************************************************************************************************

      # Email Configuration
      - GMAIL_USER=<EMAIL>
      - GMAIL_APP_PASSWORD=gnjc pcvo ocra mffp

      # MinIO Configuration
      - S3_ENDPOINT=http://192.168.0.59:9000
      - S3_PUBLIC_URL=https://minioapi.realsoftgames.com
      - S3_ACCESS_KEY=N3S84xv2d6SkXyEY5Yo5
      - S3_SECRET_KEY=UKRst5kp2msbdzE1txz3eXK162Lc2r62Ni5Sm2GQ
      - S3_BUCKET=jimmys-bali-ink
      - S3_REGION=us-east-1
      - S3_PATH_STYLE=true
    networks:
      - bridge

networks:
  bridge:
    driver: bridge