export const STUDIO_INFO = {
  name: "<PERSON>'s Bali Ink",
  email: "<EMAIL>",
  phone: "+6287860047039",
  whatsapp: "+6287860047039",
  location: {
    name: "<PERSON>'s Bali Ink Studio",
    address: "Jl. Drupadi II No.9xx",
    area: "Seminyak, Kec. Kuta",
    city: "Denpasar",
    region: "Bali",
    country: "Indonesia",
    postalCode: "80361",
    googleMapsUrl: "https://www.google.com/maps/place/Jl.+Drupadi+II+No.9,+Seminyak,+Kec.+Kuta,+Kabupaten+Badung,+Bali+80361,+Indonesia/@-8.6840346,115.1626243,17z/",
    // For embedding the map on the website
    googleMapsEmbedUrl: "https://maps.google.com/maps?width=100%25&height=300&hl=en&q=-8.684034,115.162624+(<PERSON>'s%20Bali%20Ink)&t=&z=19&ie=UTF8&iwloc=B&output=embed",
    coordinates: {
      latitude: -8.684034,
      longitude: 115.162624
    }
  },
  social: {
    instagram: "https://instagram.com/jimmysbaliink",
    facebook: "https://facebook.com/jimmysbaliink",
    tiktok: "https://tiktok.com/@jimmysbaliink"
  },
  businessHours: {
    monday: { open: "8:30 AM", close: "11:00 PM" },
    tuesday: { open: "8:30 AM", close: "11:00 PM" },
    wednesday: { open: "8:30 AM", close: "11:00 PM" },
    thursday: { open: "8:30 AM", close: "11:00 PM" },
    friday: { open: "8:30 AM", close: "11:00 PM" },
    saturday: { open: "8:30 AM", close: "11:00 PM" },
    sunday: { open: "8:30 AM", close: "11:00 PM" }
  },
  // Helper functions
  getFullAddress: () => {
    const loc = STUDIO_INFO.location
    return `${loc.address}, ${loc.area}, ${loc.city}, ${loc.region} ${loc.postalCode}`
  },
  getFormattedBusinessHours: () => {
    return Object.entries(STUDIO_INFO.businessHours).map(([day, hours]) => ({
      day: day.charAt(0).toUpperCase() + day.slice(1),
      hours: `${hours.open} - ${hours.close}`
    }))
  }
}