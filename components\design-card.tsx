import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatIDR } from '@/lib/utils'
import { getImageUrl } from '@/app/lib/file-utils'

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  }
  category: {
    _id: string
    name: string
  }
  tags: {
    _id: string
    name: string
  }[]
  media: {
    url: string
    isPrimary: boolean
  }[]
  status: 'draft' | 'live'
}

interface Props {
  design: Design
  onTagClick: (tagName: string, e: React.MouseEvent) => void
  onCategoryClick: (categoryName: string, e: React.MouseEvent) => void
}

export function DesignCard({
  design,
  onTagClick,
  onCategoryClick,
}: Props) {
  return (
    <Card className="overflow-hidden group hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
      <CardContent className="p-0">
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={getImageUrl(design.media.find(m => m.isPrimary)?.url || design.media[0].url)}
            alt={design.title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-110"
            unoptimized={(design.media.find(m => m.isPrimary)?.url || design.media[0].url)?.includes('minioapi.realsoftgames.com') || (design.media.find(m => m.isPrimary)?.url || design.media[0].url)?.includes('minio.realsoftgames.com')}
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
        </div>
        <div className="p-4">
          <div className="space-y-3">
            <h3 className="font-semibold text-lg">{design.title}</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Price:</span>
                <span className="font-medium">{formatIDR(design.price)}</span>
              </div>
              {design.size && (
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Size:</span>
                  <span>{design.size.name}</span>
                </div>
              )}
              {design.category && (
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Category:</span>
                  <span
                    className="capitalize cursor-pointer hover:text-primary"
                    onClick={(e) => onCategoryClick(design.category.name, e)}
                  >
                    {design.category.name}
                  </span>
                </div>
              )}
              {design.tags && design.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2 pt-2 border-t">
                  {design.tags.map(tag => (
                    <Badge
                      key={tag._id}
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={(e) => onTagClick(tag.name, e)}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}