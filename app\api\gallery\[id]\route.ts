import { NextRequest, NextResponse } from 'next/server'
import { ObjectId } from 'mongodb'
import { connectToDatabase } from '@/app/lib/mongodb'
import { verifyAuth } from '@/app/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { GalleryImage } from '@/app/lib/mongodb/schema'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const db = await connectToDatabase()
    const image = await db.collection(COLLECTIONS.GALLERY)
      .findOne({ _id: new ObjectId(params.id) })

    if (!image) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      ...image,
      _id: image._id.toString()
    })
  } catch (error) {
    console.error('Error in gallery GET [id]:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await verifyAuth()
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await connectToDatabase()
    const data = await request.json()

    const updateData = {
      ...data,
      updatedAt: new Date()
    }

    const result = await db.collection(COLLECTIONS.GALLERY)
      .updateOne(
        { _id: new ObjectId(params.id) },
        { $set: updateData }
      )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Image updated successfully' })
  } catch (error) {
    console.error('Error in gallery PATCH [id]:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await verifyAuth()
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await connectToDatabase()
    const result = await db.collection(COLLECTIONS.GALLERY)
      .deleteOne({ _id: new ObjectId(params.id) })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Image deleted successfully' })
  } catch (error) {
    console.error('Error in gallery DELETE [id]:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authHeader = request.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]
    const user = await verifyAuth()
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const db = await connectToDatabase()
    const data = await request.json()

    // Validate required fields
    if (!data.imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      )
    }

    if (!data.category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      )
    }

    // Verify that the category exists
    const categoryDoc = await db.collection(COLLECTIONS.CATEGORIES)
      .findOne({ name: data.category })

    if (!categoryDoc) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    // Store tags as plain strings
    const tags = data.tags || []

    const updateData = {
      imageUrl: data.imageUrl,
      category: categoryDoc.name,
      tags: tags,
      isFeatured: data.isFeatured || false,
      featuredOrder: data.isFeatured ? (data.featuredOrder || 0) : null,
      updatedAt: new Date()
    }

    const result = await db.collection(COLLECTIONS.GALLERY).updateOne(
      { _id: new ObjectId(params.id) },
      { $set: updateData }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Gallery image not found' },
        { status: 404 }
      )
    }

    // Format the response
    const responseData = {
      ...updateData,
      _id: params.id,
      category: categoryDoc.name,
      tags: tags
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error in gallery PUT:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    )
  }
} 