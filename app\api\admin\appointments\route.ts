import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { sendEmail } from '@/app/lib/email'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { ObjectId } from 'mongodb'
import { format } from 'date-fns'

export async function GET(request: NextRequest) {
  try {
    const user = await verifyAuth()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const db = await getDb()
    const appointments = await db.collection(COLLECTIONS.APPOINTMENTS).find().sort({ createdAt: -1 }).toArray()

    // Process appointments to adjust dates for display
    const processedAppointments = appointments.map(appointment => {
      // Create a copy of the appointment
      const processedAppointment = { ...appointment }

      // Ensure date is in ISO format for consistency
      if (processedAppointment.date) {
        // No need to adjust the date - we'll display it as is
        const appointmentDate = new Date(processedAppointment.date)
        processedAppointment.date = appointmentDate.toISOString()
        console.log('Appointment date for list:', processedAppointment.date)
      }

      // Convert ObjectId to string
      return {
        ...processedAppointment,
        _id: processedAppointment._id.toString()
      }
    })

    return NextResponse.json(processedAppointments)
  } catch (error) {
    console.error('Error fetching appointments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch appointments' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await verifyAuth()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const data = await request.json()
    const db = await getDb()

    // Handle date timezone issues if date is provided
    let processedData = { ...data }
    if (data.date) {
      // Parse the date string from the request
      const dateString = data.date
      console.log('Original date string from admin:', dateString)

      // Extract the date components from the ISO string
      // The format is YYYY-MM-DDTHH:mm:ss.sssZ
      const dateParts = dateString.split('T')[0].split('-')
      const year = parseInt(dateParts[0])
      const month = parseInt(dateParts[1]) - 1 // Months are 0-indexed in JavaScript
      const day = parseInt(dateParts[2])

      console.log(`Extracted date parts (admin): year=${year}, month=${month}, day=${day}`)

      // Create a date object with the extracted parts
      // Add 1 to the day to compensate for timezone difference
      // This is a specific fix for the timezone issue between client and server
      const adjustedDay = day + 1
      console.log(`Adjusted day (admin): ${adjustedDay} (original: ${day})`)

      const appointmentDate = new Date(Date.UTC(year, month, adjustedDay))
      console.log('Final admin appointment date (UTC):', appointmentDate.toISOString())

      processedData.date = appointmentDate
    }

    const result = await db.collection(COLLECTIONS.APPOINTMENTS).insertOne({
      ...processedData,
      createdAt: new Date(),
      updatedAt: new Date()
    })

    // Send confirmation email
    await sendEmail({
      to: data.email,
      subject: "Appointment Confirmation - Jimmy's Bali Ink",
      html: `
        <h1>Appointment Confirmation</h1>
        <p>Dear ${data.name},</p>
        <p>Your appointment with Jimmy's Bali Ink has been confirmed. Here are your appointment details:</p>
        <ul>
          <li>Date: ${format(new Date(data.date), 'dd/MM/yyyy')}</li>
          <li>Time: ${data.time}</li>
          ${data.design ? `<li>Design: ${data.design}</li>` : ''}
          ${data.description ? `<li>Description: ${data.description}</li>` : ''}
        </ul>
        <p>We look forward to seeing you!</p>
      `
    })

    return NextResponse.json({
      _id: result.insertedId.toString(),
      ...data
    })
  } catch (error) {
    console.error('Error creating appointment:', error)
    return NextResponse.json(
      { error: 'Failed to create appointment' },
      { status: 500 }
    )
  }
}