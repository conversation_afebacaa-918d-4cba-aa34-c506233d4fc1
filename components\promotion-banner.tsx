"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Countdown } from "@/components/ui/countdown"

interface Promotion {
  _id: string
  title: string
  description: string
  price?: number
  startDate: Date
  endDate: Date
  ctaText: string
  ctaLink: string
  isActive: boolean
  showAsModal?: boolean
}

export function PromotionBanner() {
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPromotions = async () => {
      try {
        const response = await fetch("/api/promotions")
        const data = await response.json()

        // Filter active promotions that are not modal
        const activePromotions = data.filter((promo: Promotion) => {
          const now = new Date()
          const start = new Date(promo.startDate)
          const end = new Date(promo.endDate)
          return promo.isActive && !promo.showAsModal && now >= start && now <= end
        })

        setPromotions(activePromotions)
      } catch (error) {
        console.error("Error fetching promotions:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchPromotions()
  }, [])

  useEffect(() => {
    if (promotions.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((current) => (current + 1) % promotions.length)
      }, 5000)
      return () => clearInterval(interval)
    }
  }, [promotions.length])

  const handlePrevious = () => {
    setCurrentIndex((current) =>
      current === 0 ? promotions.length - 1 : current - 1
    )
  }

  const handleNext = () => {
    setCurrentIndex((current) =>
      (current + 1) % promotions.length
    )
  }

  if (loading || promotions.length === 0) {
    return null
  }

  const currentPromo = promotions[currentIndex]

  return (
    <div className="relative z-[5] bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="container flex items-center justify-between gap-4 py-1">
        {promotions.length > 1 && (
          <Button
            variant="ghost"
            size="icon"
            className="flex-shrink-0 w-6 h-6 hover:bg-accent"
            onClick={handlePrevious}
          >
            <ChevronLeft className="w-3 h-3" />
          </Button>
        )}

        <div className="flex flex-col items-center justify-center flex-1 gap-1 px-4 text-center md:flex-row md:gap-4">
          <p className="text-xs font-medium">{currentPromo.title}</p>
          <p className="text-xs text-muted-foreground">{currentPromo.description}</p>
          <div className="flex items-center gap-2">
            <Countdown
              endDate={new Date(currentPromo.endDate)}
              className="text-xs text-muted-foreground"
            />
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-2 text-xs"
              asChild
            >
              <a href={currentPromo.ctaLink}>{currentPromo.ctaText}</a>
            </Button>
          </div>
        </div>

        {promotions.length > 1 && (
          <Button
            variant="ghost"
            size="icon"
            className="flex-shrink-0 w-6 h-6 hover:bg-accent"
            onClick={handleNext}
          >
            <ChevronRight className="w-3 h-3" />
          </Button>
        )}
      </div>

      {promotions.length > 1 && (
        <div className="absolute bottom-0.5 left-1/2 transform -translate-x-1/2 flex gap-1">
          {promotions.map((_, index) => (
            <button
              key={index}
              className={cn(
                "w-1 h-1 rounded-full transition-colors",
                index === currentIndex ? "bg-foreground" : "bg-muted"
              )}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>
      )}
    </div>
  )
}