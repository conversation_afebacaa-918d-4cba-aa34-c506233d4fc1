import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Simple cache to avoid repeated processing for the same URL
const thumbnailCache = new Map<string, { data: Buffer, contentType: string, timestamp: number }>();

// Cache expiration time: 24 hours
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

// Function to clean up old cache entries
function cleanupCache() {
  const now = Date.now();
  for (const [key, value] of thumbnailCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRATION) {
      thumbnailCache.delete(key);
    }
  }
}

// Clean up cache every hour
setInterval(cleanupCache, 60 * 60 * 1000);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');
    const width = parseInt(searchParams.get('width') || '100', 10);
    const height = parseInt(searchParams.get('height') || '100', 10);
    const quality = parseInt(searchParams.get('quality') || '80', 10);
    
    // Get timestamp for cache busting if provided
    const timestamp = searchParams.get('t');
    
    // Get bypass cache parameter
    const bypassCache = searchParams.get('bypass_cache') === 'true';

    if (!url) {
      return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
    }

    // Only allow proxying from approved MinIO domains
    if (!url.includes('minioapi.realsoftgames.com') &&
        !url.includes('minio.realsoftgames.com')) {
      console.error(`Thumbnail request rejected - Invalid domain: ${url}`);
      return NextResponse.json({ error: 'Invalid URL domain' }, { status: 403 });
    }

    // Create a cache key that includes the dimensions and timestamp if provided
    const cacheKey = `${url}_${width}x${height}_q${quality}${timestamp ? `_t${timestamp}` : ''}`;

    // Check cache first (unless bypass_cache is true)
    if (!bypassCache && thumbnailCache.has(cacheKey)) {
      const cachedData = thumbnailCache.get(cacheKey)!;
      console.log(`Serving cached thumbnail for URL: ${url}`);
      
      return new NextResponse(cachedData.data, {
        status: 200,
        headers: {
          'Content-Type': cachedData.contentType,
          'Cache-Control': 'public, max-age=86400',
          'Access-Control-Allow-Origin': '*',
          'X-Cache': 'HIT'
        }
      });
    }

    // Ensure URL starts with https://
    let normalizedUrl = url;
    if (!normalizedUrl.startsWith('http')) {
      normalizedUrl = `https://${normalizedUrl}`;
    }

    // Convert http to https for MinIO URLs
    if (normalizedUrl.startsWith('http://') && 
        (normalizedUrl.includes('minioapi.realsoftgames.com') || 
         normalizedUrl.includes('minio.realsoftgames.com'))) {
      normalizedUrl = normalizedUrl.replace('http://', 'https://');
    }

    console.log(`Processing thumbnail for: ${normalizedUrl}`);

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    try {
      // Try to fetch directly from the server where MinIO is running
      let serverUrl = normalizedUrl
        .replace('https://minioapi.realsoftgames.com', 'http://192.168.16.2:9000')
        .replace('http://minioapi.realsoftgames.com', 'http://192.168.16.2:9000');

      let response;
      let fetchMethod = 'server'; // Track which fetch method succeeded

      try {
        // First try the direct server URL
        response = await fetch(serverUrl, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          }
        });
        
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        
        console.log(`Direct server fetch succeeded for thumbnail: ${serverUrl}`);
      } catch (directError) {
        console.log(`Direct server fetch failed for thumbnail: ${directError.message}, trying original URL`);
        fetchMethod = 'original';
        
        // If that fails, try the original URL
        response = await fetch(normalizedUrl, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch from origin: ${response.status} ${response.statusText}`);
        }
        
        console.log(`Original URL fetch succeeded for thumbnail: ${normalizedUrl}`);
      }

      clearTimeout(timeoutId);

      // Get the content type from the original response
      const contentType = response.headers.get('content-type') || 'application/octet-stream';
      
      // Get the image data as an array buffer
      const imageBuffer = Buffer.from(await response.arrayBuffer());
      
      // Process the image with sharp to create a thumbnail
      const thumbnailBuffer = await sharp(imageBuffer)
        .resize(width, height, {
          fit: 'cover',
          position: 'centre'
        })
        .jpeg({ quality })
        .toBuffer();
      
      console.log(`Generated ${width}x${height} thumbnail for: ${url}`);

      // Store in cache
      thumbnailCache.set(cacheKey, {
        data: thumbnailBuffer,
        contentType: 'image/jpeg', // We're always converting to JPEG
        timestamp: Date.now()
      });

      // Create a new response with the thumbnail data and appropriate headers
      const thumbnailResponse = new NextResponse(thumbnailBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'image/jpeg',
          'Cache-Control': 'public, max-age=86400',
          'Access-Control-Allow-Origin': '*',
          'X-Cache': 'MISS'
        }
      });

      return thumbnailResponse;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error(`Thumbnail generation error for URL ${url}:`, fetchError);
      
      // Try to use a fallback approach - redirect to the proxy API
      try {
        const proxyUrl = `/api/proxy?url=${encodeURIComponent(url)}${timestamp ? `&t=${timestamp}` : ''}`;
        console.log(`Thumbnail generation failed, redirecting to proxy: ${proxyUrl}`);
        
        return NextResponse.redirect(new URL(proxyUrl, request.url), 307);
      } catch (fallbackError) {
        console.error(`Fallback approach failed:`, fallbackError);
        
        // Return a more detailed error response
        return NextResponse.json(
          { 
            error: fetchError instanceof Error ? fetchError.message : 'Fetch error',
            url: url
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error('Error in thumbnail route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
