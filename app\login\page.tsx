"use client"

import { useState } from "react"
import { useR<PERSON>er, useSearchPara<PERSON> } from "next/navigation"
import Link from "next/link"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  })
  const [errors, setErrors] = useState({
    email: "",
    password: "",
    form: ""
  })

  const validateForm = () => {
    const newErrors = {
      email: "",
      password: "",
      form: ""
    }
    let isValid = true

    if (!formData.email) {
      newErrors.email = "Email is required"
      isValid = false
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
      isValid = false
    }

    if (!formData.password) {
      newErrors.password = "Password is required"
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Validation Error", {
        description: "Please check the form for errors and try again."
      })
      return
    }

    setLoading(true)
    setErrors({ email: "", password: "", form: "" })

    const toastId = toast.loading("Logging in...")

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        console.error("Login error:", data.error)
        
        const errorMessage = data.error === "Invalid email or password"
          ? "Invalid email or password. Please check your credentials and try again."
          : data.error || "An error occurred while trying to log in. Please try again."
        
        setErrors(prev => ({
          ...prev,
          form: errorMessage
        }))
        
        toast.error("Login failed", {
          id: toastId,
          description: errorMessage
        })
        setLoading(false)
        return
      }

      // Store the JWT token
      localStorage.setItem('token', data.token)
      
      toast.success("Welcome back!", {
        id: toastId,
        description: "You have successfully logged in."
      })

      // Small delay to show the success message before redirecting
      setTimeout(() => {
        router.push("/")
        router.refresh()
      }, 500)
    } catch (error) {
      console.error('Login error:', error)
      
      const errorMessage = "An unexpected error occurred. Please try again."
      
      setErrors(prev => ({
        ...prev,
        form: errorMessage
      }))
      
      toast.error("Login failed", {
        id: toastId,
        description: errorMessage
      })
      setLoading(false)
    }
  }

  return (
    <div className="container relative min-h-[calc(100vh-4rem)] flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-1 lg:px-0">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <Card>
          <CardHeader>
            <CardTitle>Login</CardTitle>
            <CardDescription>
              Enter your email and password to access your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              {errors.form && (
                <div className="p-3 rounded-md bg-destructive/15 text-destructive text-sm">
                  {errors.form}
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => {
                    setFormData({ ...formData, email: e.target.value })
                    setErrors(prev => ({ ...prev, email: "", form: "" }))
                  }}
                  required
                  autoComplete="email"
                  disabled={loading}
                  className={errors.email ? "border-destructive" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => {
                    setFormData({ ...formData, password: e.target.value })
                    setErrors(prev => ({ ...prev, password: "", form: "" }))
                  }}
                  required
                  autoComplete="current-password"
                  disabled={loading}
                  className={errors.password ? "border-destructive" : ""}
                />
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password}</p>
                )}
              </div>

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Logging in...
                  </>
                ) : (
                  "Login"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
        <div className="text-center text-sm">
          <Link href="/forgot-password" className="text-primary hover:underline">
            Forgot your password?
          </Link>
        </div>
        <div className="text-center text-sm">
          Don&apos;t have an account?{" "}
          <Link href="/register" className="text-primary hover:underline">
            Register
          </Link>
        </div>
      </div>
    </div>
  )
}
