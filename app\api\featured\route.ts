import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

const MAX_FEATURED_PER_TYPE = 3;

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET() {
  try {
    const db = await connectToDatabase()

    // Fetch featured works from the designs collection
    const designs = await db.collection(COLLECTIONS.DESIGNS)
      .find({ isFeatured: true })
      .sort({ featuredOrder: 1 })
      .limit(MAX_FEATURED_PER_TYPE * 3)
      .toArray()

    // Separate designs by category
    const customDesigns = designs.filter(design => design.internalCategory === 'custom')
    const flashDesigns = designs.filter(design => design.internalCategory === 'flash')
    const coverUps = designs.filter(design => design.internalCategory === 'coverup')

    // Transform the data for frontend use
    const featured = {
      custom: customDesigns.map(design => ({
        _id: design._id.toString(),
        name: design.name,
        description: design.description,
        type: 'custom',
        imageUrl: design.imageUrl || '',
        price: design.price || 0,
        size: design.size || '',
        category: design.internalCategory || 'custom',
        featuredOrder: design.featuredOrder,
        createdAt: design.createdAt,
        updatedAt: design.updatedAt
      })),
      flash: flashDesigns.map(design => ({
        _id: design._id.toString(),
        name: design.name,
        description: design.description,
        type: 'flash',
        imageUrl: design.imageUrl || '',
        price: design.price || 0,
        size: design.size || '',
        category: design.internalCategory || 'flash',
        featuredOrder: design.featuredOrder,
        createdAt: design.createdAt,
        updatedAt: design.updatedAt
      })),
      coverup: coverUps.map(design => ({
        _id: design._id.toString(),
        name: design.name,
        description: design.description,
        type: 'coverup',
        imageUrl: design.imageUrl || '',
        price: design.price || 0,
        size: design.size || '',
        category: design.internalCategory || 'coverup',
        beforeImageUrl: design.beforeImageUrl,
        afterImageUrl: design.afterImageUrl,
        featuredOrder: design.featuredOrder,
        createdAt: design.createdAt,
        updatedAt: design.updatedAt
      }))
    }

    return NextResponse.json(featured)
  } catch (error) {
    console.error('Error fetching featured works:', error)
    return NextResponse.json(
      { error: 'Failed to fetch featured works' },
      { status: 500 }
    )
  }
}