"use client"

import { useState, useEffect } from "react"
import { Tag } from "@/types"
import { ItemManager } from "../shared/ItemManager"
import { ItemDialog } from "../shared/ItemDialog"
import { DeleteConfirmDialog } from "../shared/DeleteConfirmDialog"
import { toast } from "sonner"

export default function TagsPage() {
  const [tags, setTags] = useState<Tag[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [editingTag, setEditingTag] = useState<Tag | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)

  useEffect(() => {
    fetchTags()
  }, [])

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/tags')
      if (!response.ok) throw new Error('Failed to fetch tags')
      const data = await response.json()
      setTags(data)
    } catch (error) {
      console.error('Error fetching tags:', error)
      toast.error('Failed to fetch tags')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (tag: Tag) => {
    setEditingTag(tag)
    setDialogOpen(true)
  }

  const handleDelete = (id: string) => {
    setDeletingId(id)
    setDeleteDialogOpen(true)
  }

  const handleSubmit = async (formData: { name: string; description?: string }) => {
    try {
      setSubmitting(true)
      const token = localStorage.getItem('token')
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      if (editingTag) {
        const response = await fetch(`/api/tags?id=${editingTag._id}`, {
          method: 'PUT',
          headers,
          body: JSON.stringify(formData)
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to update tag')
        }

        await fetchTags()
        toast.success('Tag updated successfully')
      } else {
        const response = await fetch('/api/tags', {
          method: 'POST',
          headers,
          body: JSON.stringify(formData)
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to create tag')
        }

        const newTag = await response.json()
        setTags(prev => [...prev, newTag].sort((a, b) => (a.order || 0) - (b.order || 0)))
        toast.success('Tag created successfully')
      }

      setDialogOpen(false)
      setEditingTag(null)
    } catch (error) {
      console.error('Error saving tag:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save tag')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!deletingId) return

    try {
      const token = localStorage.getItem('token')
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      const response = await fetch(`/api/tags?id=${deletingId}`, {
        method: 'DELETE',
        headers
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete tag')
      }

      setTags(tags.filter(tag => tag._id !== deletingId))
      setDeleteDialogOpen(false)
      setDeletingId(null)
      toast.success('Tag deleted successfully')
    } catch (error) {
      console.error('Error deleting tag:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete tag')
    }
  }

  const handleReorder = async (reorderedTags: Tag[]) => {
    try {
      const response = await fetch('/api/tags/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tags: reorderedTags })
      })
      if (!response.ok) throw new Error('Failed to update tag order')
      setTags(reorderedTags)
    } catch (error) {
      console.error('Error reordering tags:', error)
      // Revert to original order if failed
      fetchTags()
    }
  }

  return (
    <div className="container mx-auto py-6">
      <ItemManager
        title="Tags"
        description="Manage your design tags"
        items={tags}
        loading={loading}
        onAddClick={() => {
          setEditingTag(null);
          setDialogOpen(true);
        }}
        onEditClick={handleEdit}
        onDeleteClick={handleDelete}
        onReorder={handleReorder}
        sortable={true}
      />

      <ItemDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSubmit={handleSubmit}
        title={editingTag ? "Edit Tag" : "Add Tag"}
        submitLabel={editingTag ? "Save" : "Add"}
        initialData={editingTag || undefined}
        loading={submitting}
      />

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Tag"
        description="Are you sure you want to delete this tag? This action cannot be undone."
      />
    </div>
  )
}