import { NextResponse } from 'next/server'

export function GET() {
  return NextResponse.json(
    { error: 'Upload API error occurred', message: 'This is a fallback response for upload API route' },
    { status: 500 }
  )
}

export function POST() {
  return NextResponse.json(
    { error: 'Upload API error occurred', message: 'This is a fallback response for upload API route' },
    { status: 500 }
  )
}

export function PUT() {
  return NextResponse.json(
    { error: 'Upload API error occurred', message: 'This is a fallback response for upload API route' },
    { status: 500 }
  )
}

export function DELETE() {
  return NextResponse.json(
    { error: 'Upload API error occurred', message: 'This is a fallback response for upload API route' },
    { status: 500 }
  )
}
