/**
 * Utility functions for handling files and images
 */
import { existsSync } from 'fs';

/**
 * Get the appropriate URL for an image based on the environment
 *
 * Use proxy for MinIO URLs to avoid CORS and SSL issues
 *
 * @param url The original MinIO URL
 * @param options Optional parameters for URL processing
 * @returns The URL to use for loading the image
 */
export function getImageUrl(url: string, options: {
  cacheBust?: boolean,
  bypassCache?: boolean,
  isAppointmentImage?: boolean
} = {}): string {
  if (!url) return '';

  // Handle data URLs (used for previews) directly
  if (url.startsWith('data:')) {
    return url;
  }

  // Clean up the URL if it contains double slashes (except after protocol)
  if (url.includes('//') && !url.startsWith('http')) {
    url = url.replace(/([^:])\/{2,}/g, '$1/');
  }

  // Use proxy for MinIO URLs
  if (url.includes('minioapi.realsoftgames.com') ||
      url.includes('minio.realsoftgames.com')) {

    console.log(`Processing MinIO URL in file-utils: ${url}`);

    // Make sure the URL uses HTTPS
    if (url.startsWith('http://')) {
      url = url.replace('http://', 'https://');
      console.log(`Converted to HTTPS: ${url}`);
    }

    // Add https:// if the URL doesn't have a protocol
    if (!url.startsWith('http')) {
      url = `https://${url}`;
      console.log(`Added protocol: ${url}`);
    }

    // Check if this is an appointment image
    if (options.isAppointmentImage || url.includes('/appointments/')) {
      console.log(`Processing appointment image: ${url}`);
    }

    // Log the URL being processed
    console.log(`Processing MinIO URL: ${url}`);

    // Build the proxy URL with appropriate parameters
    let proxyUrl = `/api/proxy?url=${encodeURIComponent(url)}`;

    // Add cache busting if requested
    if (options.cacheBust) {
      const timestamp = Date.now();
      proxyUrl += `&t=${timestamp}`;
      console.log(`Added cache busting to URL: ${proxyUrl}`);
    }

    // Add bypass cache parameter if requested
    if (options.bypassCache) {
      proxyUrl += `&bypass_cache=true`;
      console.log(`Added bypass cache to URL: ${proxyUrl}`);
    }

    console.log(`Converted to proxy URL: ${proxyUrl}`);
    return proxyUrl;
  }

  return url;
}

/**
 * Check if a URL is from MinIO storage
 * @param url The URL to check
 * @returns True if the URL is from MinIO storage
 */
export function isMinioUrl(url: string): boolean {
  return url.includes('minioapi.realsoftgames.com') ||
         url.includes('minio.realsoftgames.com');
}

/**
 * Get a valid file extension from a URL or filename
 * @param url The URL or filename to extract the extension from
 * @param defaultExtension The default extension to use if none is found or invalid
 * @returns A valid file extension
 */
export function getValidFileExtension(url: string, defaultExtension: string = 'jpg'): string {
  // Extract file extension from the URL or default to the provided default
  const fileExtension = url.split('.').pop()?.toLowerCase() || defaultExtension;

  // List of valid image extensions
  const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

  // Return the extension if valid, otherwise return the default
  return validExtensions.includes(fileExtension) ? fileExtension : defaultExtension;
}

/**
 * Create a filename with a valid extension for email attachments
 * @param baseName The base name for the file
 * @param url The URL or filename to extract the extension from
 * @param defaultExtension The default extension to use if none is found or invalid
 * @returns A filename with a valid extension
 */
export function createAttachmentFilename(
  baseName: string,
  url: string,
  defaultExtension: string = 'jpg'
): string {
  const extension = getValidFileExtension(url, defaultExtension);
  return `${baseName}.${extension}`;
}

/**
 * Create an email attachment object with the correct filename
 * @param url The URL of the file
 * @param index The index of the attachment (for naming)
 * @param prefix The prefix to use for the filename
 * @param defaultExtension The default extension to use if none is found or valid
 * @returns An email attachment object
 */
export function createEmailAttachment(
  url: string,
  index: number,
  prefix: string = 'attachment',
  defaultExtension: string = 'jpg'
): {
  filename: string;
  path: string;
  cid: string;
} {
  const extension = getValidFileExtension(url, defaultExtension);
  const baseName = `${prefix}-${index + 1}`;

  try {
    // Handle different path formats
    let fullPath;

    // Check if the URL is a MinIO URL
    if (isMinioUrl(url)) {
      // For MinIO URLs, use the URL directly
      console.log('Using MinIO URL for attachment:', url);
      fullPath = url;
    } else {
      // Ensure the URL starts with a slash if it doesn't already
      const normalizedUrl = url.startsWith('/') ? url : `/${url}`;

      if (normalizedUrl.startsWith('/uploads/')) {
        // For uploads paths, use the persistent volume path
        fullPath = `/app${normalizedUrl}`;
        console.log('Using uploads path for attachment:', fullPath);

        // If the file doesn't exist in the uploads directory, try the public directory
        if (!existsSync(fullPath)) {
          const publicPath = process.cwd() + '/public' + normalizedUrl;
          console.log('File not found in uploads directory, trying public path:', publicPath);

          if (existsSync(publicPath)) {
            console.log('File found in public directory');
            fullPath = publicPath;
          }
        }
      } else {
        // For other paths, use the public directory
        fullPath = process.cwd() + '/public' + normalizedUrl;
        console.log('Using public directory path for attachment:', fullPath);
      }
    }

    return {
      filename: `${baseName}.${extension}`,
      path: fullPath,
      cid: baseName // Content ID for embedding in HTML
    };
  } catch (error) {
    console.error('Error creating email attachment:', error);
    // Return a fallback that won't break the email sending process
    return {
      filename: `${baseName}.${extension}`,
      path: '', // Empty path will be handled by nodemailer
      cid: baseName
    };
  }
}
