# MinIO Integration for Jimmy's Bali Ink

This document provides information about the MinIO integration for the Jimmy's Bali Ink application.

## Overview

The application now uses MinIO for storing and serving uploaded files, including:
- Gallery images
- Design images
- Appointment reference images
- Any other uploaded content

## Configuration

The application connects to MinIO using environment variables defined in the `.env` file:

```
# Use direct HTTP connection internally
S3_ENDPOINT=http://192.168.0.59:9000
# Use HTTPS URL for public access
S3_PUBLIC_URL=https://minioapi.realsoftgames.com
S3_ACCESS_KEY=N3S84xv2d6SkXyEY5Yo5
S3_SECRET_KEY=UKRst5kp2msbdzE1txz3eXK162Lc2r62Ni5Sm2GQ
S3_BUCKET=jimmys-bali-ink
S3_REGION=us-east-1
S3_PATH_STYLE=true
```

**Note:**
- The application uses HTTP internally for direct connections to MinIO (S3_ENDPOINT)
- It uses HTTPS externally for public access URLs (S3_PUBLIC_URL)
- This configuration works well with a reverse proxy that handles SSL termination
- These credentials are from a MinIO service account specifically created for this application
- The credentials are stored in the `.env` file which is loaded by the application
- The `.env` file is included in the Docker container via the `env_file` directive in `docker-compose.yml`

## Bucket Setup

The application automatically creates the required bucket (`jimmys-bali-ink`) if it doesn't exist when the container starts. This is handled by the script that runs before the Next.js application starts.

You can also manually create the bucket using:

```bash
npm run create-bucket
```

## Directory Structure

The application organizes files in the MinIO bucket using the following directory structure:

```
jimmys-bali-ink/
├── gallery/         # Gallery images
├── designs/         # Design images
├── appointments/    # Appointment reference images
├── contact/         # Contact form attachments
```

Each directory corresponds to a different type of upload in the application. This structure helps keep files organized and makes it easier to manage permissions and access control if needed in the future.

## Migration

To migrate existing uploads from the local file system to MinIO, run:

```bash
npm run migrate-to-minio
```

This script will:
1. Scan the local uploads directory for files
2. Upload each file to MinIO
3. Update the database references to point to the MinIO URLs

## Troubleshooting

If you encounter issues with file uploads or access:

1. Check the MinIO API server is accessible:
   ```bash
   curl http://minioapi.realsoftgames.com
   ```

2. Verify the bucket exists:
   ```bash
   npm run create-bucket
   ```

3. Check the application logs for any S3/MinIO related errors:
   ```bash
   docker logs jimmys-bali-ink
   ```

4. Ensure the MinIO credentials are correct in the environment variables.

## Security Considerations

- The bucket is configured with a public read policy to allow direct access to uploaded files
- Write access is restricted to the application using the configured credentials
- Consider implementing signed URLs for more sensitive content if needed in the future
