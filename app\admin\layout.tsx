"use client"

import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { Menu, X } from "lucide-react"
import { Button } from "@/components/ui/button"

const navigationItems = [
  { href: "/admin", label: "Dashboard" },
  { href: "/admin/appointments", label: "Appointments" },
  { href: "/admin/gallery", label: "Gallery" },
  { href: "/admin/designs", label: "Designs" },
  { href: "/admin/categories", label: "Categories" },
  { href: "/admin/tags", label: "Tags" },
  { href: "/admin/sizes", label: "Sizes" },
  { href: "/admin/promotions", label: "Promotions" },
  { href: "/admin/users", label: "Users" },
]

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Handle access_token from email links
  useEffect(() => {
    const accessToken = searchParams.get('access_token');
    if (accessToken) {
      console.log('Found access_token in URL, storing it');
      // Store the token in localStorage for future API requests
      localStorage.setItem('token', accessToken);

      // Also set it as a cookie for middleware authentication
      document.cookie = `token=${accessToken}; path=/; secure; samesite=lax`;

      // Remove the token from the URL to prevent sharing/bookmarking issues
      // This is done by replacing the current URL without the query parameter
      const url = new URL(window.location.href);
      url.searchParams.delete('access_token');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="flex-1 flex flex-col min-h-screen">
      <nav className="border-b bg-background sticky top-0 z-10">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            <Link href="/admin" className="font-bold text-lg">Admin Dashboard</Link>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                aria-label="Toggle menu"
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>

            {/* Desktop navigation */}
            <div className="hidden md:flex md:space-x-4 overflow-x-auto">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium hover:bg-accent hover:text-accent-foreground whitespace-nowrap ${
                    (item.href === "/admin" && pathname === "/admin") ||
                    (item.href !== "/admin" && pathname.startsWith(item.href))
                      ? "bg-primary/10 text-primary"
                      : "text-foreground/80"
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-base font-medium ${
                    (item.href === "/admin" && pathname === "/admin") ||
                    (item.href !== "/admin" && pathname.startsWith(item.href))
                      ? "bg-primary/10 text-primary"
                      : "text-foreground/80 hover:bg-accent hover:text-accent-foreground"
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>
      <main className="flex-1 p-4">{children}</main>
    </div>
  )
}