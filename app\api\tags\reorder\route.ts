import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { tags } = body

    if (!tags || !Array.isArray(tags)) {
      return NextResponse.json(
        { error: 'Invalid tags data' },
        { status: 400 }
      )
    }

    const db = await connectToDatabase()
    
    // Update each tag's order in the database
    const bulkOps = tags.map((tag, index) => ({
      updateOne: {
        filter: { _id: new ObjectId(tag._id) },
        update: { 
          $set: { 
            order: index,
            updatedAt: new Date()
          }
        }
      }
    }))

    await db.collection(COLLECTIONS.TAGS).bulkWrite(bulkOps)

    // Fetch the updated tags to return
    const updatedTags = await db.collection(COLLECTIONS.TAGS)
      .find({ _id: { $in: tags.map(t => new ObjectId(t._id)) } })
      .sort({ order: 1 })
      .toArray()

    return NextResponse.json({
      message: 'Tag order updated successfully',
      tags: updatedTags.map(t => ({
        ...t,
        _id: t._id.toString()
      }))
    })
  } catch (error) {
    console.error('Error updating tag order:', error)
    return NextResponse.json(
      { error: 'Failed to update tag order' },
      { status: 500 }
    )
  }
} 