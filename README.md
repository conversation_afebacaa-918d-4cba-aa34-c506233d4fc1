# JimmysBaliInk

## Build Steps

1. Install dependencies and build the Next.js application:
```bash
# Install dependencies
npm install

# Build the Next.js application
npm run build
```

2. Build the Docker image:
```bash
# Build with proper tags
docker build -t jimmys-bali-ink:latest .
```

3. Tag for private registry:
```bash
# Tag for your private registry
docker tag jimmys-bali-ink:latest 192.168.0.245:6500/jimmys-bali-ink:latest
```

4. Push to private registry:
```bash
# Push to private registry
docker push 192.168.0.245:6500/jimmys-bali-ink:latest
```

## Important Notes

- Ensure your private registry (192.168.0.245:6500) is accessible
- The build machine must have Docker installed and configured
- Node.js 18.x LTS is recommended for the build process
- Make sure the jimmysbaliink volume exists on the target server
- The application expects /app/public/uploads directory for file storage

## Troubleshooting

If build fails:
1. Clear build cache: `rm -rf .next`
2. Delete node_modules: `rm -rf node_modules`
3. Clean npm cache: `npm cache clean --force`
4. Retry build process from step 1

If image push fails:
1. Verify registry connection: `curl http://192.168.0.245:6500/v2/_catalog`
2. Ensure Docker is logged into private registry
3. Check registry permissions
