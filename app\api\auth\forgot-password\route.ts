import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabaseForReset } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { sendEmail } from '@/app/lib/email'
import jwt from 'jsonwebtoken'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    const { db } = await connectToDatabaseForReset()
    const user = await db.collection(COLLECTIONS.USERS).findOne({ email })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Generate reset token
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET is not defined')
    }

    const token = jwt.sign(
      { userId: user._id.toString() },
      secret,
      { expiresIn: '1h' }
    )

    // Save token to user
    await db.collection(COLLECTIONS.USERS).updateOne(
      { _id: user._id },
      {
        $set: {
          resetPasswordToken: token,
          resetPasswordExpires: new Date(Date.now() + 3600000), // 1 hour
          updatedAt: new Date()
        }
      }
    )

    // Send reset email with improved template
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${token}`
    await sendEmail({
      to: email,
      subject: "Reset Your Password - Jimmy's Bali Ink",
      html: `
        <h1>Reset Your Password</h1>
        <p>Hello ${user.firstName},</p>
        <p>We received a request to reset your password. Click the link below to set a new password:</p>
        <p><a href="${resetUrl}">${resetUrl}</a></p>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this password reset, you can safely ignore this email.</p>
        <p>Best regards,<br>Jimmy's Bali Ink Team</p>
      `
    })

    return NextResponse.json({ message: 'Reset email sent' })
  } catch (error) {
    console.error('Error in forgot-password:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 