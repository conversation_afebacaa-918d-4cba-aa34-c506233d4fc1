import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { getDb } from '@/app/lib/mongodb'
import { signEdgeJWT } from '@/lib/edge-auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      )
    }

    const db = await getDb()
    const users = db.collection('users')

    const user = await users.findOne({ email: email.toLowerCase() })
    console.log('Login attempt - Found user:', { 
      ...user, 
      _id: user?._id?.toString(),
      password: '[REDACTED]' 
    })

    if (!user) {
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      )
    }

    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      )
    }

    // Update last login
    await users.updateOne(
      { _id: user._id },
      { $set: { lastLogin: new Date() } }
    )

    // Create JWT token with all necessary user data
    const tokenPayload = {
      userId: user._id.toString(),
      email: user.email,
      firstName: user.firstName,
      surname: user.surname,
      role: user.role
    }

    console.log('Login successful - Token payload:', tokenPayload)
    const token = await signEdgeJWT(tokenPayload)

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user
    return NextResponse.json({
      message: "Login successful",
      token,
      user: {
        ...userWithoutPassword,
        id: user._id
      }
    })
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: "Failed to login. Please try again." },
      { status: 500 }
    )
  }
} 