"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Loader2, ChevronLeft, X } from 'lucide-react'
import { DraggableImageArray } from '../../../components/DraggableImageArray'
import { SelectWithDescription } from '@/components/ui/select-with-description'
import { getImageUrl } from '@/app/lib/file-utils'
import Image from 'next/image'

interface MediaFile {
  url: string
  previewUrl: string
}

interface Tag {
  _id: string
  name: string
  description?: string
  order: number
}

interface Category {
  _id: string
  name: string
  description?: string
  order: number
}

interface FormData {
  imageUrl: string
  category: {
    _id: string
    name: string
  }
  tags: string[]
  isFeatured: boolean
}

export default function NewGalleryImage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [uploadingImage, setUploadingImage] = useState(false)
  const [tags, setTags] = useState<Tag[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [formData, setFormData] = useState<FormData>({
    imageUrl: '',
    category: { _id: '', name: '' },
    tags: [],
    isFeatured: false
  })

  useEffect(() => {
    fetchTags()
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to fetch categories')
    }
  }

  const fetchTags = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/tags', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch tags')
      }

      const data = await response.json()
      setTags(data)
    } catch (error) {
      console.error('Error fetching tags:', error)
      toast.error('Failed to fetch tags')
    }
  }

  const handleTagChange = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter(id => id !== tagId)
        : [...prev.tags, tagId]
    }))
  }

  const handleRemoveImage = () => {
    setFormData(prev => ({
      ...prev,
      imageUrl: ''
    }))
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setUploadingImage(true)

    try {
      const file = files[0] // Only take the first file since we don't need multiple

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} is not an image file`)
        return
      }

      // Create preview
      const previewUrl = await new Promise<string>((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => {
          resolve(reader.result as string)
        }
        reader.readAsDataURL(file)
      })

      // Upload file
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)
      uploadFormData.append('type', 'gallery')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: uploadFormData
      })

      if (!response.ok) {
        throw new Error(`Failed to upload ${file.name}`)
      }

      const data = await response.json()
      setFormData(prev => ({
        ...prev,
        imageUrl: data.filePath
      }))

      toast.success('Image uploaded successfully')
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    } finally {
      setUploadingImage(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      if (!formData.imageUrl) {
        throw new Error('Please upload an image')
      }

      if (!formData.category.name) {
        throw new Error('Please select a category')
      }

      const response = await fetch('/api/gallery', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          imageUrl: formData.imageUrl,
          category: formData.category.name,
          tags: formData.tags,
          isFeatured: formData.isFeatured
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create gallery image')
      }

      toast.success('Gallery image added successfully')
      router.push('/admin/gallery')
    } catch (error) {
      console.error('Error creating gallery image:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create gallery image')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="h-10 px-4 py-2"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold">Add New Gallery Image</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Image Upload</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Upload Image</Label>
                {formData.imageUrl && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveImage}
                    className="h-8 px-2"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                )}
              </div>
              {formData.imageUrl ? (
                <div className="relative aspect-[3/2] overflow-hidden rounded-lg border">
                  <div className="relative w-full h-full">
                    <Image
                      src={getImageUrl(formData.imageUrl)}
                      alt="Gallery preview"
                      fill
                      className="object-cover"
                      unoptimized={formData.imageUrl?.includes('minioapi.realsoftgames.com') || formData.imageUrl?.includes('minio.realsoftgames.com')}
                    />
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center border rounded-lg p-4">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    disabled={uploadingImage}
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>Category</Label>
              <SelectWithDescription
                items={categories}
                value={formData.category._id}
                onValueChange={(value) => {
                  const selectedCategory = categories.find(c => c._id === value)
                  if (selectedCategory) {
                    setFormData(prev => ({
                      ...prev,
                      category: {
                        _id: selectedCategory._id,
                        name: selectedCategory.name
                      }
                    }))
                  }
                }}
                placeholder="Select category"
              />
            </div>

            <div className="space-y-4">
              <Label>Tags</Label>
              <div className="space-y-4">
                <SelectWithDescription
                  items={tags.filter(tag => !formData.tags.includes(tag._id))}
                  onValueChange={handleTagChange}
                  placeholder="Add tags"
                />

                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tagId) => {
                      const tag = tags.find(t => t._id === tagId)
                      if (!tag) return null

                      return (
                        <div
                          key={tagId}
                          className="flex items-center gap-2 bg-secondary px-3 py-1 rounded-full"
                        >
                          <span>{tag.name}</span>
                          <button
                            type="button"
                            onClick={() => handleTagChange(tagId)}
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Featured Image</Label>
              <div className="flex items-center gap-2">
                <Switch
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({ ...prev, isFeatured: checked }))
                  }
                />
                <span className="text-sm text-muted-foreground">
                  {formData.isFeatured ? 'Featured - Will appear in featured section' : 'Not featured'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Button
          type="submit"
          className="w-full"
          disabled={loading || uploadingImage || !formData.imageUrl}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding to Gallery...
            </>
          ) : (
            'Add to Gallery'
          )}
        </Button>
      </form>
    </div>
  )
}