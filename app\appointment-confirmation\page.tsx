"use client"

import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { CheckCircle2, AlertTriangle, Calendar, Clock, User, Mail, Phone, FileText, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { STUDIO_INFO } from "@/lib/config"
import { format } from "date-fns"
import { ViewOnlyImageGrid } from "@/components/ViewOnlyImageGrid"

interface AppointmentDetails {
  _id: string
  fullName: string
  email: string
  phone: string
  date: string
  time: string
  serviceType: {
    _id: string
    name: string
    slug: string
  }
  size: {
    _id: string
    name: string
    description: string
  }
  description?: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  media?: Array<{
    url: string
    previewUrl: string
  }>
  createdAt: string
}

export default function AppointmentConfirmation() {
  const searchParams = useSearchParams()

  const emailFailed = searchParams.get('emailFailed') === 'true'
  const appointmentId = searchParams.get('appointmentId')

  const [appointment, setAppointment] = useState<AppointmentDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (appointmentId) {
      setLoading(true)
      fetch(`/api/appointments/${appointmentId}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch appointment details')
          }
          return response.json()
        })
        .then(data => {
          setAppointment(data)
          setLoading(false)
        })
        .catch(err => {
          console.error('Error fetching appointment:', err)
          setError('Unable to load appointment details')
          setLoading(false)
        })
    }
  }, [appointmentId])
  return (
    <div className="container mx-auto py-16 px-4">
      <div className="max-w-2xl mx-auto">
        <Card className="text-center">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <CheckCircle2 className="h-16 w-16 text-green-500" />
            </div>
            <CardTitle className="text-3xl mb-2">Appointment Request Received!</CardTitle>
            <CardDescription className="text-lg">
              Thank you for choosing Jimmy's Bali Ink
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {emailFailed && (
              <Alert className="text-left bg-yellow-50 border-yellow-200">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Email Delivery Issue</AlertTitle>
                <AlertDescription>
                  We couldn't send a confirmation email to your address. Please save this page or contact us directly to confirm your appointment details.
                </AlertDescription>
              </Alert>
            )}

            {loading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : error ? (
              <Alert variant="destructive" className="text-left">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error}. Please contact us directly to confirm your appointment details.
                </AlertDescription>
              </Alert>
            ) : appointment ? (
              <div className="text-left space-y-6">
                <div className="bg-muted/50 rounded-lg p-6 space-y-4">
                  <h3 className="text-lg font-medium">Appointment Details</h3>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="flex items-start space-x-3">
                      <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Date</p>
                        <p className="text-muted-foreground">
                          {format(new Date(appointment.date), "EEEE, MMMM d, yyyy")}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <Clock className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Time</p>
                        <p className="text-muted-foreground">{appointment.time}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Name</p>
                        <p className="text-muted-foreground">{appointment.fullName}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-muted-foreground">{appointment.phone}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 sm:col-span-2">
                      <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-muted-foreground">{appointment.email}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Service</p>
                        <p className="text-muted-foreground">{appointment.serviceType.name}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Size</p>
                        <p className="text-muted-foreground">{appointment.size.name}</p>
                      </div>
                    </div>

                    {appointment.description && (
                      <div className="flex items-start space-x-3 sm:col-span-2">
                        <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">Description</p>
                          <p className="text-muted-foreground">{appointment.description}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {appointment.media && appointment.media.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium">Reference Images</h3>
                    <ViewOnlyImageGrid images={appointment.media} showFullSizeButton={false} disableZoomEffect={true} />
                  </div>
                )}
              </div>
            ) : (
              <div className="text-muted-foreground">
                <p className="mb-4">
                  We have received your appointment request and will review it shortly. {!emailFailed && "You will receive a confirmation email with your appointment details."}
                </p>
              </div>
            )}

            <div className="text-muted-foreground">
              <p className="mb-4">
                If you need to make any changes or have questions about your appointment, please contact us:
              </p>
              <ul className="space-y-2 mb-6">
                <li>Email: {STUDIO_INFO.email}</li>
                <li>Phone: {STUDIO_INFO.phone}</li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild>
                <Link href="/gallery">
                  Browse Our Gallery
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/">
                  Return Home
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}