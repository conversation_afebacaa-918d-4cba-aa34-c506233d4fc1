"use client"

import { useEffect, useState } from 'react'
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { X } from "lucide-react"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"

interface Tag {
  _id: string
  name: string
  color?: string
}

interface GalleryImage {
  _id: string
  imageUrl: string
  category: string
  tags: string[]
  isFeatured: boolean
  featuredOrder?: number
}

interface GalleryImageWithTags extends Omit<GalleryImage, 'tags'> {
  tags: Tag[]
}

interface ServiceGalleryProps {
  category: string
  tag?: string
}

export function ServiceGallery({ category, tag }: ServiceGalleryProps) {
  const [images, setImages] = useState<GalleryImageWithTags[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState<GalleryImageWithTags | null>(null)
  const [tags, setTags] = useState<Map<string, Tag>>(new Map())

  useEffect(() => {
    const fetchTags = async () => {
      try {
        const response = await fetch('/api/tags')
        if (!response.ok) {
          throw new Error('Failed to fetch tags')
        }
        const data: Tag[] = await response.json()
        const tagMap = new Map(data.map((tag: Tag) => [tag._id, tag]))
        setTags(tagMap)
        return tagMap
      } catch (error) {
        console.error('Error fetching tags:', error)
        return new Map<string, Tag>()
      }
    }

    const fetchImages = async (tagMap: Map<string, Tag>) => {
      try {
        const params = new URLSearchParams()
        if (category) params.append('category', category)
        if (tag) params.append('tag', tag)
        
        const response = await fetch(`/api/gallery?${params.toString()}`)
        if (!response.ok) throw new Error('Failed to fetch gallery')
        const data = await response.json()
        
        // Map tag IDs to actual tag objects
        const imagesWithTags = data.map((image: GalleryImage) => ({
          ...image,
          tags: (image.tags || []).map(tagId => tagMap.get(tagId)).filter(Boolean)
        }))
        setImages(imagesWithTags)
      } catch (error) {
        console.error('Error fetching gallery:', error)
      } finally {
        setLoading(false)
      }
    }

    const initData = async () => {
      const tagMap = await fetchTags()
      await fetchImages(tagMap)
    }

    initData()
  }, [category, tag])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="aspect-square bg-muted animate-pulse rounded-lg" />
        ))}
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center h-48">
          <p className="text-muted-foreground">Gallery coming soon...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="relative">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {images.map((image) => (
              <CarouselItem key={image._id} className="pl-2 md:pl-4 basis-full sm:basis-1/2 md:basis-1/3">
                <Card 
                  className="group relative overflow-hidden cursor-pointer"
                  onClick={() => setSelectedImage(image)}
                >
                  {image.isFeatured && (
                    <Badge 
                      variant="default"
                      className="absolute top-2 right-2 z-10 bg-black/80 hover:bg-black/90 text-white border border-white/20 text-xs font-medium"
                    >
                      Featured
                    </Badge>
                  )}
                  <CardContent className="p-0">
                    <div className="aspect-square relative">
                      <Image
                        src={image.imageUrl}
                        alt={`${image.category} tattoo design`}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                      <div className="text-white space-y-2">
                        <p className="text-lg font-semibold capitalize">{image.category}</p>
                        <div className="flex flex-wrap gap-2">
                          {image.tags.map((tag) => (
                            <Badge 
                              key={tag._id} 
                              variant="outline" 
                              className="text-white border-white/40 hover:border-white/60 bg-black/20"
                              style={tag.color ? { backgroundColor: tag.color } : undefined}
                            >
                              {tag.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="absolute -left-12 top-1/2 -translate-y-1/2" />
          <CarouselNext className="absolute -right-12 top-1/2 -translate-y-1/2" />
        </Carousel>
      </div>

      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden">
          <button
            onClick={() => setSelectedImage(null)}
            className="absolute top-2 right-2 z-50 p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors"
          >
            <X className="h-6 w-6 text-white" />
          </button>
          {selectedImage && (
            <div className="relative w-full h-[95vh]">
              <Image
                src={selectedImage.imageUrl}
                alt={`${selectedImage.category} tattoo design`}
                fill
                className="object-contain"
                priority
              />
              <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-4">
                <p className="text-white font-medium mb-2 capitalize">{selectedImage.category}</p>
                <div className="flex flex-wrap gap-2">
                  {selectedImage.tags.map((tag) => (
                    <Badge 
                      key={tag._id} 
                      variant="outline" 
                      className="text-white border-white"
                      style={tag.color ? { backgroundColor: tag.color } : undefined}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
} 