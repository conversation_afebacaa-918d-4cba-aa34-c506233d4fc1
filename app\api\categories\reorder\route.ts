import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { ObjectId } from 'mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { categories } = body

    if (!categories || !Array.isArray(categories)) {
      return NextResponse.json(
        { error: 'Invalid categories data' },
        { status: 400 }
      )
    }

    const db = await getDb()

    // Update each category's order in the database
    const bulkOps = categories.map((category, index) => ({
      updateOne: {
        filter: { _id: new ObjectId(category._id) },
        update: {
          $set: {
            order: index,
            updatedAt: new Date()
          }
        }
      }
    }))

    await db.collection(COLLECTIONS.CATEGORIES).bulkWrite(bulkOps)

    // Fetch the updated categories to return
    const updatedCategories = await db.collection(COLLECTIONS.CATEGORIES)
      .find({ _id: { $in: categories.map(c => new ObjectId(c._id)) } })
      .sort({ order: 1 })
      .toArray()

    return NextResponse.json({
      message: 'Category order updated successfully',
      categories: updatedCategories.map(c => ({
        ...c,
        _id: c._id.toString()
      }))
    })
  } catch (error) {
    console.error('Error updating category order:', error)
    return NextResponse.json(
      { error: 'Failed to update category order' },
      { status: 500 }
    )
  }
}