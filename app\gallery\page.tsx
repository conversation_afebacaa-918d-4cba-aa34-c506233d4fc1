"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Search } from "lucide-react"
import { toast } from "sonner"
import { ImageModal } from "@/components/ui/image-modal"
import { COLLECTIONS } from "@/app/lib/mongodb/collections"
import { GalleryCard } from "@/components/gallery-card"

interface Category {
  _id: string
  name: string
  description?: string
  order: number
}

interface Tag {
  _id: string
  name: string
}

// Display interface for frontend use
interface GalleryItemDisplay {
  _id: string
  name?: string
  description?: string
  imageUrl: string
  category: Category | string | null
  isFeatured: boolean
  featuredOrder: number
  tags: Tag[]
  createdAt: Date
  updatedAt: Date
}

export default function GalleryPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [galleryItems, setGalleryItems] = useState<GalleryItemDisplay[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [selectedImageTitle, setSelectedImageTitle] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch both categories and gallery items
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Fetch categories first
        const categoriesResponse = await fetch('/api/categories')

        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories')
        }
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData)

        // Then fetch gallery items
        const galleryResponse = await fetch('/api/gallery')

        if (!galleryResponse.ok) {
          throw new Error('Failed to fetch gallery items')
        }
        const data = await galleryResponse.json()

        // Map the data with proper relationships
        setGalleryItems(data.map((item: any) => ({
          _id: item._id,
          name: item.name,
          description: item.description,
          imageUrl: item.imageUrl,
          category: item.category,
          isFeatured: Boolean(item.isFeatured),
          featuredOrder: item.featuredOrder || 0,
          tags: Array.isArray(item.tags) ? item.tags.map((tag: { _id: string; name: string }) => ({
            _id: tag._id,
            name: tag.name
          })) : [],
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        })))
      } catch (error) {
        console.error('Error fetching data:', error)
        setError('Failed to load gallery data')
        toast.error("Failed to load gallery data")
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Get all categories including "All" option
  const allCategories = [
    { _id: "all", name: "All Works", order: -1 },
    ...categories.sort((a, b) => (a.order || 0) - (b.order || 0))
  ]

  const handleCategoryClick = (categoryId: string) => {
    setActiveTab(categoryId)
    setSearchQuery("")
  }

  const handleTagClick = (tagName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setSearchQuery(tagName)
    setActiveTab("all") // Reset to all works when searching by tag
  }

  const handleCategoryNameClick = (categoryName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setSearchQuery(categoryName)
    setActiveTab("all") // Reset to all works when searching by category name
  }

  // Filter gallery items based on active tab and search query
  const filteredItems = galleryItems.filter(item => {
    // Category filtering
    const matchesCategory = activeTab === "all" ||
      (item.category &&
        ((typeof item.category === 'object' && item.category._id === activeTab) ||
         (typeof item.category === 'string' && item.category === categories.find(c => c._id === activeTab)?.name)))

    // Search filtering
    const searchLower = searchQuery.toLowerCase()
    const matchesSearch = searchQuery === "" ||
      // Search in name/description if they exist
      (item.name?.toLowerCase().includes(searchLower) || false) ||
      (item.description?.toLowerCase().includes(searchLower) || false) ||
      // Search in category
      (typeof item.category === 'object' && item.category?.name?.toLowerCase().includes(searchLower)) ||
      (typeof item.category === 'string' && item.category.toLowerCase().includes(searchLower)) ||
      // Search in tags
      (item.tags || []).some(tag => tag.name.toLowerCase().includes(searchLower))

    return matchesCategory && matchesSearch
  })

  // Sort items by featured status and order
  const sortedItems = [...filteredItems].sort((a, b) => {
    // First sort by featured status
    if (a.isFeatured && !b.isFeatured) return -1
    if (!a.isFeatured && b.isFeatured) return 1

    // If both are featured, sort by featuredOrder
    if (a.isFeatured && b.isFeatured) {
      return (a.featuredOrder || 0) - (b.featuredOrder || 0)
    }

    // If neither are featured, sort by creation date
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })

  if (error) {
    return (
      <div className="container mx-auto py-16 px-4 text-center">
        <p className="text-red-500">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Our Gallery</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
          Explore our collection of unique tattoo designs and completed works
        </p>
        <div className="max-w-md mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search by name, category, or tags..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full" value={activeTab} onValueChange={handleCategoryClick}>
        <TabsList className="flex justify-center mb-8 flex-wrap gap-2">
          {allCategories.map((category) => (
            <TabsTrigger key={category._id} value={category._id}>
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((n) => (
                <Card key={n} className="overflow-hidden animate-pulse">
                  <div className="aspect-square bg-muted" />
                  <CardFooter className="p-4">
                    <div className="w-full space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-3 bg-muted rounded w-1/2" />
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {sortedItems.map((item) => (
                <GalleryCard
                  key={item._id}
                  {...item}
                  onImageClick={() => {
                    setSelectedImage(item.imageUrl)
                    setSelectedImageTitle(item.name || 'View Image')
                  }}
                  onTagClick={handleTagClick}
                  onCategoryClick={handleCategoryNameClick}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
      <ImageModal
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        src={selectedImage || ""}
        alt={selectedImageTitle}
      />
    </div>
  )
}
