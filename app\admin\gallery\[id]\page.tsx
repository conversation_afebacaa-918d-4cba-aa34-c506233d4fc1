"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { SelectWithDescription } from "@/components/ui/select-with-description"
import { toast } from "sonner"
import { Loader2, X } from "lucide-react"
import { getImageUrl } from "@/app/lib/file-utils"

interface Category {
  _id: string
  name: string
  description?: string
}

interface Tag {
  _id: string
  name: string
  color?: string
}

interface GalleryImage {
  _id: string
  imageUrl: string
  category: {
    _id: string
    name: string
  } | string
  tags: string[]
  isFeatured: boolean
}

export default function EditGalleryImage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [formData, setFormData] = useState<GalleryImage>({
    _id: '',
    imageUrl: '',
    category: { _id: '', name: '' },
    tags: [],
    isFeatured: false
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories')
        if (!categoriesResponse.ok) throw new Error('Failed to fetch categories')
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData)

        // Fetch tags
        const tagsResponse = await fetch('/api/tags')
        if (!tagsResponse.ok) throw new Error('Failed to fetch tags')
        const tagsData = await tagsResponse.json()
        setTags(tagsData)

        // Fetch gallery image
        const imageResponse = await fetch(`/api/gallery/${params.id}`)
        if (!imageResponse.ok) throw new Error('Failed to fetch image')
        const imageData = await imageResponse.json()

        // Find the category object that matches the image's category name
        const categoryObj = categoriesData.find((c: Category) => c.name === imageData.category)

        // Clean tag IDs to remove ObjectId wrapper if present
        const cleanTags = (imageData.tags || []).map((tag: string) => {
          // If tag is in ObjectId format, extract just the ID
          if (tag.includes('ObjectId')) {
            return tag.replace(/ObjectId\(['"](.+)['"]\)/, '$1')
          }
          return tag
        })

        setFormData({
          _id: imageData._id,
          imageUrl: imageData.imageUrl,
          category: categoryObj ? { _id: categoryObj._id, name: categoryObj.name } : '',
          tags: cleanTags,
          isFeatured: imageData.isFeatured || false
        })
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Failed to load data')
      }
    }

    fetchData()
  }, [params.id])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const categoryName = typeof formData.category === 'object' ? formData.category.name : formData.category

      const response = await fetch(`/api/gallery/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          ...formData,
          category: categoryName,
          tags: formData.tags.map(tag => tag.replace(/ObjectId\(['"](.+)['"]\)/, '$1'))
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update gallery image')
      }

      toast.success('Gallery image updated successfully')
      router.push('/admin/gallery')
    } catch (error) {
      console.error('Error updating gallery image:', error)
      toast.error('Failed to update gallery image')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Edit Gallery Image</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="max-w-[400px] mx-auto">
              <div className="aspect-square relative mb-6 rounded-lg overflow-hidden">
                <Image
                  src={getImageUrl(formData.imageUrl)}
                  alt="Gallery image"
                  fill
                  className="object-cover"
                  unoptimized={formData.imageUrl?.includes('minioapi.realsoftgames.com') || formData.imageUrl?.includes('minio.realsoftgames.com')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Category</Label>
              <SelectWithDescription
                items={categories.map(cat => ({
                  ...cat,
                  value: cat._id,
                  label: cat.name
                }))}
                value={typeof formData.category === 'object' ? formData.category._id : ''}
                onValueChange={(value) => {
                  const category = categories.find(c => c._id === value)
                  if (category) {
                    setFormData(prev => ({
                      ...prev,
                      category: { _id: category._id, name: category.name }
                    }))
                  }
                }}
                placeholder="Select category"
              />
            </div>

            <div className="space-y-4">
              <Label>Tags</Label>
              <div className="space-y-4">
                <SelectWithDescription
                  items={tags.filter(tag => !formData.tags.includes(tag._id))}
                  onValueChange={(value) => {
                    setFormData(prev => ({
                      ...prev,
                      tags: [...prev.tags, value]
                    }))
                  }}
                  placeholder="Add tags"
                />

                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tagId) => {
                      const tag = tags.find(t => t._id === tagId)
                      if (!tag) return null

                      return (
                        <div
                          key={tagId}
                          className="flex items-center gap-2 bg-secondary px-3 py-1 rounded-full"
                        >
                          <span>{tag.name}</span>
                          <button
                            type="button"
                            onClick={() => {
                              setFormData(prev => ({
                                ...prev,
                                tags: prev.tags.filter(id => id !== tagId)
                              }))
                            }}
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="featured"
                checked={formData.isFeatured}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
              />
              <Label htmlFor="featured">Featured Image</Label>
            </div>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/admin/gallery')}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}