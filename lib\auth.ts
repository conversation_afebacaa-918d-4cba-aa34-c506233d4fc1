import jwt from 'jsonwebtoken'
import { getDb } from '@/app/lib/mongodb'
import { ObjectId } from 'mongodb'
import clientPromise from './mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export interface JWTPayload {
  userId: string
  email: string
  firstName: string
  surname: string
  role: string
  purpose?: string
}

export function signJWT(payload: JWTPayload): string {
  return jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: '30d' // Token expires in 30 days
  })
}

export function verifyJWT(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload
  } catch (error) {
    console.error('JWT verification error:', error)
    return null
  }
}

export function decodeJWT(token: string): JWTPayload | null {
  try {
    return jwt.decode(token) as JWTPayload
  } catch (error) {
    console.error('JWT decode error:', error)
    return null
  }
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export interface AuthResult {
  success: boolean
  userId?: string
  role?: string
}

export async function verifyAuth(token?: string, requireAdmin = false): Promise<AuthResult> {
  if (!token) {
    return { success: false }
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload

    // Special handling for system tokens (from email links)
    if (decoded.userId === 'system' && decoded.purpose === 'email-link') {
      // For system tokens, we trust the role in the token
      if (requireAdmin && decoded.role !== 'admin') {
        return { success: false }
      }

      return {
        success: true,
        userId: decoded.userId,
        role: decoded.role
      }
    }

    // For regular user tokens, verify against the database
    const db = await getDb()
    const user = await db.collection(COLLECTIONS.USERS).findOne({
      _id: new ObjectId(decoded.userId)
    })

    if (!user) {
      return { success: false }
    }

    if (requireAdmin && user.role !== 'admin') {
      return { success: false }
    }

    return {
      success: true,
      userId: user._id.toString(),
      role: user.role
    }
  } catch (error) {
    console.error('Auth verification error:', error)
    return { success: false }
  }
}

// Special database connection for password reset that returns db object
export async function connectToDatabaseForReset() {
  const client = await clientPromise
  const db = client.db(process.env.MONGODB_DB || 'jimmysbaliink')
  return { db }
}