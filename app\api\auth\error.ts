import { NextResponse } from 'next/server'

export function GET() {
  return NextResponse.json(
    { error: 'Auth API error occurred', message: 'This is a fallback response for auth API routes' },
    { status: 500 }
  )
}

export function POST() {
  return NextResponse.json(
    { error: 'Auth API error occurred', message: 'This is a fallback response for auth API routes' },
    { status: 500 }
  )
}

export function PUT() {
  return NextResponse.json(
    { error: 'Auth API error occurred', message: 'This is a fallback response for auth API routes' },
    { status: 500 }
  )
}

export function DELETE() {
  return NextResponse.json(
    { error: 'Auth API error occurred', message: 'This is a fallback response for auth API routes' },
    { status: 500 }
  )
}
