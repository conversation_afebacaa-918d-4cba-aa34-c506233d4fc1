import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from '@/components/theme-provider'
import { Navbar } from '@/components/navbar'
import { Providers } from './providers'
import { Toaster } from '@/components/ui/toaster'
import { cn } from '@/lib/utils'
import LayoutWrapper from './layout-wrapper'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Jimmy\'s Bali Ink',
  description: 'Professional tattoo studio offering custom designs and expert artistry',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, "min-h-screen bg-background antialiased flex flex-col")}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>
            <Navbar />
            <main className="flex-1 w-full">
              <LayoutWrapper>
                {children}
              </LayoutWrapper>
            </main>
            <footer className="w-full py-2 border-t bg-background">
              <div className="container flex flex-col items-center justify-between gap-2 md:h-8 md:flex-row">
                <p className="text-xs text-muted-foreground">
                  © {new Date().getFullYear()} Jimmy's Bali Ink. All rights reserved.
                </p>
                <nav className="flex items-center gap-4 text-xs">
                  <a href="/privacy" className="text-muted-foreground hover:underline">
                    Privacy
                  </a>
                  <a href="/terms" className="text-muted-foreground hover:underline">
                    Terms
                  </a>
                </nav>
              </div>
            </footer>
            <Toaster />
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  )
}
