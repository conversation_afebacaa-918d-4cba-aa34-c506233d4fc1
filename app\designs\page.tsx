"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { toast } from 'sonner'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search, X } from 'lucide-react'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DesignCard } from '@/components/design-card'

interface Tag {
  _id: string
  name: string
  description?: string
  order: number
}

interface Category {
  _id: string
  name: string
  description?: string
  order: number
}

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  }
  category: {
    _id: string
    name: string
  }
  tags: {
    _id: string
    name: string
  }[]
  media: {
    url: string
    isPrimary: boolean
  }[]
  status: 'draft' | 'live'
  createdAt: string
}

export default function DesignsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState(searchParams.get('category') || "all")
  const [searchQuery, setSearchQuery] = useState("")
  const [designs, setDesigns] = useState<Design[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [sizes, setSizes] = useState<{ _id: string; name: string }[]>([])
  const [tags, setTags] = useState<{ _id: string; name: string }[]>([])
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [selectedImageTitle, setSelectedImageTitle] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchCategories()
    fetchDesigns()
    fetchSizes()
    fetchTags()
  }, [router])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      if (!response.ok) {
        if (response.status === 401) {
          // Handle unauthorized silently for categories
          return
        }
        throw new Error('Failed to fetch categories')
      }
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to fetch categories')
    }
  }

  const fetchSizes = async () => {
    try {
      const response = await fetch('/api/sizes')
      if (!response.ok) {
        if (response.status === 401) {
          // Handle unauthorized silently for sizes
          return
        }
        throw new Error('Failed to fetch sizes')
      }
      const data = await response.json()
      setSizes(data)
    } catch (error) {
      console.error('Error fetching sizes:', error)
      toast.error('Failed to fetch sizes')
    }
  }

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/tags')
      if (!response.ok) {
        if (response.status === 401) {
          // Handle unauthorized silently for tags
          return
        }
        throw new Error('Failed to fetch tags')
      }
      const data = await response.json()
      setTags(data)
    } catch (error) {
      console.error('Error fetching tags:', error)
      toast.error('Failed to fetch tags')
    }
  }

  const fetchDesigns = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Add retry logic with exponential backoff
      const check = async (retries = 3, delay = 1000) => {
        try {
          console.log('Attempting to fetch designs...')
          const response = await fetch('/api/designs')

          if (!response.ok) {
            throw new Error(`Failed to fetch designs: ${response.status} ${response.statusText}`)
          }

          const data = await response.json()
          console.log(`Successfully fetched ${data.length} designs`)
          return data
        } catch (error) {
          console.error('Error fetching designs:', error)

          if (retries > 0) {
            console.log(`Retrying in ${delay}ms... (${retries} retries left)`)
            await new Promise(resolve => setTimeout(resolve, delay))
            return check(retries - 1, delay * 2)
          }

          throw error
        }
      }

      const data = await check()
      setDesigns(data.filter((design: Design) => design.status === 'live'))
    } catch (error) {
      console.error('Error fetching designs:', error)
      setError('Failed to load designs. Please try refreshing the page.')
      toast.error("Failed to load designs")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategoryClick = (categoryId: string) => {
    setActiveTab(categoryId)
    setSearchQuery("")
    router.push(`/designs?category=${categoryId}`)
  }

  const handleTagClick = (tagName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setSearchQuery(tagName)
    setActiveTab("all")
    router.push('/designs')
  }

  const handleCategoryNameClick = (categoryName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setSearchQuery(categoryName)
    setActiveTab("all")
    router.push('/designs')
  }

  // Filter designs based on active tab and search query
  const filteredDesigns = designs.filter(design => {
    // Category filtering
    const matchesCategory = activeTab === "all" || design.category?._id === activeTab

    // Search filtering
    const searchLower = searchQuery.toLowerCase()
    const matchesSearch = searchQuery === "" ||
      // Search in title/description
      design.title.toLowerCase().includes(searchLower) ||
      (design.description?.toLowerCase().includes(searchLower) || false) ||
      // Search in category
      design.category?.name.toLowerCase().includes(searchLower) ||
      // Search in tags
      design.tags.some(tag => tag.name.toLowerCase().includes(searchLower))

    return matchesCategory && matchesSearch
  })

  // Sort designs by creation date (newest first)
  const sortedDesigns = [...filteredDesigns].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  // Add "All" to categories and sort them by order
  const allCategories = [
    { _id: "all", name: "All Designs", description: "", order: -1 },
    ...categories.sort((a, b) => (a.order || 0) - (b.order || 0))
  ]

  const getSizeName = (sizeId: string) => {
    const size = sizes.find(s => s._id === sizeId)
    return size?.name || ''
  }

  const getTagName = (tagId: string) => {
    const tag = tags.find(t => t._id === tagId)
    return tag?.name || ''
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-16 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-16 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Our Designs</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
          Browse our collection of unique tattoo designs
        </p>
        <div className="max-w-md mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search by name, category, or tags..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleCategoryClick} className="space-y-8">
        <TabsList className="w-full flex justify-center overflow-x-auto">
          {allCategories.map((category) => (
            <TabsTrigger
              key={category._id}
              value={category._id}
              className="capitalize"
            >
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="m-0">
          {sortedDesigns.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {sortedDesigns.map((design) => (
                <DesignCard
                  key={design._id}
                  design={design}
                  onTagClick={handleTagClick}
                  onCategoryClick={handleCategoryNameClick}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No designs found matching your criteria.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}