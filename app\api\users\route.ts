import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { verifyAuth } from '@/lib/auth'
import bcrypt from 'bcryptjs'

function censorEmail(email: string): string {
  const [localPart, domain] = email.split('@')
  const censoredLocal = localPart.charAt(0) + '*'.repeat(localPart.length - 1)
  return `${censoredLocal}@${domain}`
}

export async function GET(request: NextRequest) {
  try {
    const user = await verifyAuth()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const db = await connectToDatabase()
    const users = await db.collection(COLLECTIONS.USERS).find().toArray()

    return NextResponse.json(users.map(user => ({
      ...user,
      _id: user._id.toString()
    })))
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')

    // Get the current user's ID from the token
    const authResult = await verifyAuth(token, true)
    if (!authResult.success || !authResult.userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const { email, name, role, confirmPassword } = await request.json()

    // Determine if user is admin based on role field
    const isAdmin = role === 'admin'

    console.log('Update user request:', {
      id,
      email,
      name,
      role,
      isAdmin
    })

    if (!id || !email || !confirmPassword) {
      return NextResponse.json(
        { error: 'Email and password confirmation are required' },
        { status: 400 }
      )
    }

    const db = await connectToDatabase()

    // Verify the current user's password
    const adminUser = await db.collection(COLLECTIONS.USERS).findOne({
      _id: new ObjectId(authResult.userId)
    })

    if (!adminUser) {
      return NextResponse.json(
        { error: 'Admin user not found' },
        { status: 404 }
      )
    }

    const isPasswordValid = await bcrypt.compare(confirmPassword, adminUser.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid password' },
        { status: 401 }
      )
    }

    // Always ensure the current user is an admin when modifying users
    console.log('Admin check:', {
      currentUserRole: adminUser.role,
      isAdmin: isAdmin
    })

    if (adminUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can modify users' },
        { status: 403 }
      )
    }

    // Prevent removing the last admin
    if (isAdmin === false) {
      const targetUser = await db.collection(COLLECTIONS.USERS).findOne({
        _id: new ObjectId(id)
      })

      console.log('Last admin check:', {
        targetUserId: id,
        targetUserRole: targetUser?.role,
        isRemovingAdmin: targetUser?.role === 'admin' && isAdmin === false
      })

      if (targetUser?.role === 'admin') {
        const adminCount = await db.collection(COLLECTIONS.USERS).countDocuments({
          role: 'admin'
        })

        console.log('Admin count:', adminCount)

        if (adminCount <= 1) {
          return NextResponse.json(
            { error: 'Cannot remove the last administrator' },
            { status: 400 }
          )
        }
      }
    }

    // Prepare update data
    const updateData = {
      email,
      role: isAdmin ? 'admin' : 'user',
      updatedAt: new Date(),
      modifiedBy: {
        userId: adminUser._id.toString(),
        name: adminUser.name,
        timestamp: new Date().toISOString()
      }
    }

    console.log('Updating user with data:', updateData)

    const result = await db.collection(COLLECTIONS.USERS).updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get token from Authorization header
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')

    // Verify admin authorization
    const isAuthorized = await verifyAuth(token, true)
    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const db = await connectToDatabase()
    const result = await db.collection(COLLECTIONS.USERS).deleteOne({
      _id: new ObjectId(id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}