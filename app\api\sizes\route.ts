import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { ObjectId } from 'mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Helper function to extract dimensions from size name
function getDimensionsFromName(name: string): number {
  // Extract numbers from the size name (e.g., "2x2", "5x7")
  const dimensions = name.match(/\d+/g)
  if (!dimensions) return 0

  // Calculate area (e.g., 2x2=4, 5x7=35) for sorting
  const [width, height] = dimensions.map(Number)
  return width * (height || width) // If only one number found, assume square
}

export async function GET(request: NextRequest) {
  try {
    console.log('Sizes API called')
    // Allow public access for GET requests to sizes
    // Only check auth if there's a token provided
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')

    if (token) {
      console.log('Token provided:', token.substring(0, 10) + '...')
      const authResult = await verifyAuth(token)
      if (!authResult.success) {
        console.log('Auth failed')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
      console.log('Auth successful')
    } else {
      console.log('No token provided, proceeding as public access')
    }

    console.log('Getting database connection')
    const db = await getDb()
    console.log('Database connection successful')

    console.log('Fetching sizes from collection:', COLLECTIONS.SIZES)
    const sizes = await db.collection(COLLECTIONS.SIZES).find().sort({ order: 1 }).toArray()
    console.log('Sizes fetched, count:', sizes.length)

    return NextResponse.json(sizes.map(size => ({
      ...size,
      _id: size._id.toString()
    })))
  } catch (error) {
    console.error('Error fetching sizes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sizes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, order } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Size name is required' },
        { status: 400 }
      )
    }

    const db = await getDb()

    // Check if size already exists (case insensitive)
    const existingSize = await db.collection(COLLECTIONS.SIZES).findOne({
      name: { $regex: new RegExp(`^${name}$`, 'i') }
    })

    if (existingSize) {
      return NextResponse.json(
        { error: 'Size already exists' },
        { status: 400 }
      )
    }

    // Get the highest order number if not provided
    let nextOrder = order
    if (!nextOrder) {
      const lastSize = await db.collection(COLLECTIONS.SIZES)
        .findOne({}, { sort: { order: -1 } })
      nextOrder = (lastSize?.order || 0) + 1
    }

    // Create the new size object
    const newSize = {
      name,
      description: description || '',
      order: nextOrder,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: {
        userId: authResult.userId,
        role: authResult.role,
        timestamp: new Date().toISOString()
      }
    }

    const result = await db.collection(COLLECTIONS.SIZES).insertOne(newSize)

    // Return the complete size object
    const createdSize = {
      _id: result.insertedId.toString(),
      ...newSize
    }

    return NextResponse.json(createdSize)
  } catch (error) {
    console.error('Error creating size:', error)
    return NextResponse.json(
      { error: 'Failed to create size' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const isAuthorized = await verifyAuth(token, true)

    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Size ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { name, description } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Size name is required' },
        { status: 400 }
      )
    }

    const db = await getDb()

    // Check if another size with the same name exists (excluding current size)
    const existingSize = await db.collection(COLLECTIONS.SIZES).findOne({
      name,
      _id: { $ne: new ObjectId(id) }
    })

    if (existingSize) {
      return NextResponse.json(
        { error: 'Size with this name already exists' },
        { status: 400 }
      )
    }

    // Get the current size to preserve its order
    const currentSize = await db.collection(COLLECTIONS.SIZES).findOne({ _id: new ObjectId(id) })
    if (!currentSize) {
      return NextResponse.json(
        { error: 'Size not found' },
        { status: 404 }
      )
    }

    const result = await db.collection('sizes').updateOne(
      { _id: new ObjectId(id) },
      {
        $set: {
          name,
          description,
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Size not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Size updated successfully'
    })
  } catch (error) {
    console.error('Error updating size:', error)
    return NextResponse.json(
      { error: 'Failed to update size' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const isAuthorized = await verifyAuth(token, true)

    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Size ID is required' },
        { status: 400 }
      )
    }

    const db = await getDb()
    const result = await db.collection(COLLECTIONS.SIZES).deleteOne({
      _id: new ObjectId(id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Size not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Size deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting size:', error)
    return NextResponse.json(
      { error: 'Failed to delete size' },
      { status: 500 }
    )
  }
}