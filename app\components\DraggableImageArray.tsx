import { useState } from 'react'
import Image from 'next/image'
import { X } from 'lucide-react'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { getImageUrl } from '@/app/lib/file-utils'

interface MediaFile {
  url: string
  previewUrl: string
}

interface SortableImageProps {
  id: string
  url: string
  onRemove: () => void
}

function SortableImage({ id, url, onRemove }: SortableImageProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  }

  return (
    <div className="relative group">
      <div
        ref={setNodeRef}
        style={style}
        className="cursor-move border-2 border-border hover:border-primary/50 rounded-lg overflow-hidden"
        {...attributes}
        {...listeners}
      >
        <div className="relative w-32 h-32">
          <Image
            src={getImageUrl(url)}
            alt="Design preview"
            fill
            className="object-cover"
            unoptimized={url?.includes('minioapi.realsoftgames.com') || url?.includes('minio.realsoftgames.com')}
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
        </div>
      </div>
      {!transform && (
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onRemove()
          }}
          className="absolute top-1 right-1 w-6 h-6 flex items-center justify-center bg-destructive text-destructive-foreground hover:bg-destructive/90 opacity-0 group-hover:opacity-100 transition-opacity rounded-md z-50"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  )
}

interface DraggableImageArrayProps {
  images: MediaFile[]
  onReorder: (newImages: MediaFile[]) => void
  onRemove: (index: number) => void
}

export function DraggableImageArray({
  images,
  onReorder,
  onRemove
}: DraggableImageArrayProps) {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (over && active.id !== over.id) {
      const oldIndex = images.findIndex(img => img.url === active.id)
      const newIndex = images.findIndex(img => img.url === over.id)
      onReorder(arrayMove(images, oldIndex, newIndex))
    }
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={images.map(img => img.url)}
        strategy={horizontalListSortingStrategy}
      >
        <div className="flex gap-4 overflow-x-auto pb-4">
          {images.map((image, index) => (
            <SortableImage
              key={image.url}
              id={image.url}
              url={image.previewUrl || image.url}
              onRemove={() => onRemove(index)}
            />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  )
}
