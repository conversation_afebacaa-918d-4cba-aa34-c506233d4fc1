import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Initialize S3 client with MinIO configuration
if (!process.env.S3_ENDPOINT || !process.env.S3_ACCESS_KEY || !process.env.S3_SECRET_KEY) {
  console.warn('MinIO environment variables not set. Please check your .env file.');
}

// No SSL handling needed for internal HTTP connections

// Create S3 client with appropriate settings for internal HTTP connection
const s3Client = new S3Client({
  region: process.env.S3_REGION || 'us-east-1',
  endpoint: process.env.S3_ENDPOINT,
  credentials: {
    accessKeyId: process.env.S3_ACCESS_KEY || '',
    secretAccessKey: process.env.S3_SECRET_KEY || '',
  },
  forcePathStyle: process.env.S3_PATH_STYLE === 'true', // Use path style if configured
  // No need for SSL configuration since we're using HTTP internally
});

const bucketName = process.env.S3_BUCKET || 'jimmys-bali-ink';

// Upload a file to MinIO
export async function uploadFileToS3(fileBuffer: Buffer, fileName: string, contentType: string, directory?: string): Promise<string> {
  try {
    // If directory is provided, prepend it to the fileName
    const key = directory ? `${directory}/${fileName}` : fileName;

    console.log(`Uploading file to MinIO: ${key} (${contentType})`);

    const params = {
      Bucket: bucketName,
      Key: key,
      Body: fileBuffer,
      ContentType: contentType,
    };

    await s3Client.send(new PutObjectCommand(params));

    // Return the URL to the file
    // Use S3_PUBLIC_URL for public access, falling back to S3_ENDPOINT if not provided
    let publicUrl = `${process.env.S3_PUBLIC_URL || process.env.S3_ENDPOINT}/${bucketName}/${key}`;

    // No need to fix typos as we're using the correct domain

    // Make sure we're using HTTPS for public URLs
    if (publicUrl.startsWith('http://') && process.env.S3_PUBLIC_URL) {
      publicUrl = publicUrl.replace('http://', 'https://');
      console.log(`Converted to HTTPS in S3 client: ${publicUrl}`);
    }

    console.log(`File uploaded successfully to MinIO: ${publicUrl}`);
    return publicUrl;
  } catch (error) {
    console.error('Error uploading file to S3:', error);
    throw error;
  }
}

// Delete a file from MinIO
export async function deleteFileFromS3(fileName: string): Promise<void> {
  try {
    // Extract the key from the full URL if it's a URL
    let key = fileName;
    if (fileName.startsWith('http')) {
      key = getFileKeyFromUrl(fileName) || fileName;
    }

    const params = {
      Bucket: bucketName,
      Key: key,
    };

    await s3Client.send(new DeleteObjectCommand(params));
  } catch (error) {
    console.error('Error deleting file from S3:', error);
    throw error;
  }
}

// Generate a pre-signed URL for temporary access
export async function getSignedFileUrl(fileName: string, expiresIn = 3600): Promise<string> {
  try {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileName,
    });

    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw error;
  }
}

// Get the public URL for a file
export function getPublicFileUrl(fileName: string): string {
  // Use S3_PUBLIC_URL for public access, which should be configured to serve files
  // This might be different from the API endpoint used for S3 operations
  let publicUrl = `${process.env.S3_PUBLIC_URL || process.env.S3_ENDPOINT}/${bucketName}/${fileName}`;

  // No need to fix typos as we're using the correct domain

  // Make sure we're using HTTPS for public URLs
  if (publicUrl.startsWith('http://') && process.env.S3_PUBLIC_URL) {
    publicUrl = publicUrl.replace('http://', 'https://');
    console.log(`Converted to HTTPS in getPublicFileUrl: ${publicUrl}`);
  }

  return publicUrl;
}

// Extract the file key from a full URL
export function getFileKeyFromUrl(url: string): string | null {
  try {
    // Handle both full URLs and relative paths
    if (url.startsWith('http')) {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // Remove the bucket name and return the rest of the path
      return pathParts.slice(2).join('/');
    } else if (url.startsWith('/')) {
      // For paths like /uploads/gallery/image.jpg
      const pathParts = url.split('/');
      // Skip the first empty part and 'uploads'
      return pathParts.slice(2).join('/');
    }
    return null;
  } catch (error) {
    console.error('Error extracting file key from URL:', error);
    return null;
  }
}
