import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a number as IDR currency
 * @param amount The amount to format
 * @param options Intl.NumberFormatOptions to customize the formatting
 * @returns Formatted price string in IDR
 */
export function formatIDR(amount: number, options: Intl.NumberFormatOptions = {}) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    ...options
  }).format(amount)
}
