import Image from "next/image"
import { getImageUrl } from "@/app/lib/file-utils"
import Link from "next/link"

interface MediaFile {
  url: string
  previewUrl: string
}

interface ViewOnlyImageGridProps {
  images: MediaFile[]
  showFullSizeButton?: boolean
  disableZoomEffect?: boolean
}

export function ViewOnlyImageGrid({ images, showFullSizeButton = true, disableZoomEffect = false }: ViewOnlyImageGridProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {images.map((image, index) => {
        // Determine the image source - use preview URL if available, otherwise use the main URL
        const imageSource = image.previewUrl || image.url;
        // Process the URL through our utility function
        const processedUrl = getImageUrl(imageSource);

        return (
          <div
            key={image.url}
            className="relative aspect-square rounded-lg overflow-hidden border bg-muted group"
          >
            <Image
              src={processedUrl}
              alt={`Reference image ${index + 1}`}
              fill
              className={`object-cover ${disableZoomEffect ? '' : 'transition-transform duration-300 group-hover:scale-105'}`}
              unoptimized={true} // Always use unoptimized for uploaded images
              onError={(e) => {
                console.error(`Error loading image ${index + 1}:`, processedUrl);
                // Try with a direct URL as fallback
                const imgElement = e.currentTarget as HTMLImageElement;
                if (image.url && image.url !== imageSource) {
                  console.log(`Trying fallback URL for image ${index + 1}:`, image.url);
                  imgElement.src = getImageUrl(image.url);
                }
              }}
            />
            {/* Hover effect without the View Full Size button */}
            {!disableZoomEffect && (
              <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity z-10" />
            )}

            {showFullSizeButton && (
              <Link
                href={`/api/proxy?url=${encodeURIComponent(image.url)}`}
                target="_blank"
                className="absolute inset-0 flex items-center justify-center z-20"
              >
                <div className="bg-white text-black px-3 py-1 rounded-md text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                  View Full Size
                </div>
              </Link>
            )}

            <div className="absolute bottom-1 right-1 bg-background/80 px-2 py-0.5 rounded text-xs z-20">
              {index + 1}
            </div>
          </div>
        );
      })}
    </div>
  );
}
