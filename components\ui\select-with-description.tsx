'use client';

import * as React from 'react';
import * as SelectPrimitive from '@radix-ui/react-select';
import { Check, ChevronDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from './input';

interface Item {
  _id: string;
  name: string;
  description?: string;
}

interface SelectWithDescriptionProps {
  items: Item[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const SelectWithDescription = React.forwardRef<
  HTMLButtonElement,
  SelectWithDescriptionProps
>(({ items, value, onValueChange, placeholder = "Select...", disabled = false }, ref) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [open, setOpen] = React.useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const selectedItem = items.find(item => item._id === value);

  // Reset search query when items change or dropdown closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery('');
    }
  }, [open, items]);

  // Filter items based on search query
  const filteredItems = React.useMemo(() => {
    if (!searchQuery) return items;
    const query = searchQuery.toLowerCase();
    return items.filter(item => 
      item.name.toLowerCase().includes(query) || 
      item.description?.toLowerCase().includes(query)
    );
  }, [items, searchQuery]);

  // Prevent select from closing when interacting with input
  const handleInputInteraction = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
  };

  return (
    <SelectPrimitive.Root 
      value={value || undefined} 
      onValueChange={onValueChange}
      open={open}
      onOpenChange={setOpen}
    >
      <SelectPrimitive.Trigger
        ref={ref}
        className={cn(
          'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',
        )}
        disabled={disabled}
      >
        <SelectPrimitive.Value placeholder={placeholder}>
          {selectedItem?.name || ''}
        </SelectPrimitive.Value>
        <SelectPrimitive.Icon>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </SelectPrimitive.Icon>
      </SelectPrimitive.Trigger>
      
      {open && (
        <SelectPrimitive.Portal>
          <SelectPrimitive.Content
            className="relative z-50 min-w-[8rem] max-h-[300px] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
            position="popper"
            side="bottom"
            sideOffset={4}
          >
            <div className="flex items-center gap-2 p-2 border-b sticky top-0 bg-popover z-10">
              <Search className="h-4 w-4 text-muted-foreground shrink-0" />
              <Input
                ref={inputRef}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onClick={handleInputInteraction}
                onKeyDown={handleInputInteraction}
                onMouseDown={handleInputInteraction}
                placeholder="Search..."
                className="h-8 border-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
            
            <SelectPrimitive.Viewport
              className="p-1 overflow-y-auto max-h-[calc(300px-45px)] scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500"
              style={{ 
                scrollbarWidth: 'thin',
                scrollbarColor: 'var(--scrollbar-thumb) transparent'
              }}
            >
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <SelectPrimitive.Item
                    key={item._id}
                    value={item._id}
                    className="relative flex flex-col w-full cursor-default select-none items-start rounded-sm py-1.5 pl-8 pr-2 outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  >
                    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                      <SelectPrimitive.ItemIndicator>
                        <Check className="h-4 w-4" />
                      </SelectPrimitive.ItemIndicator>
                    </span>
                    <SelectPrimitive.ItemText>
                      <div>
                        <div className="font-medium">{item.name}</div>
                        {item.description && (
                          <div className="text-xs text-muted-foreground">{item.description}</div>
                        )}
                      </div>
                    </SelectPrimitive.ItemText>
                  </SelectPrimitive.Item>
                ))
              ) : (
                <div className="text-sm text-muted-foreground text-center py-6">
                  No results found
                </div>
              )}
            </SelectPrimitive.Viewport>
          </SelectPrimitive.Content>
        </SelectPrimitive.Portal>
      )}
    </SelectPrimitive.Root>
  );
});

SelectWithDescription.displayName = 'SelectWithDescription';

export { SelectWithDescription }; 