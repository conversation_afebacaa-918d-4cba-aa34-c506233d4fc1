"use client"

import { useEffect, useState } from "react"

interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

interface CountdownProps {
  endDate: Date
  className?: string
}

export function Countdown({ endDate, className }: CountdownProps) {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 })

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = new Date(endDate).getTime() - new Date().getTime()
      
      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24))
        const hours = Math.floor((difference / (1000 * 60 * 60)) % 24)
        const minutes = Math.floor((difference / 1000 / 60) % 60)
        const seconds = Math.floor((difference / 1000) % 60)
        
        setTimeLeft({ days, hours, minutes, seconds })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }

    calculateTimeLeft()
    const timer = setInterval(calculateTimeLeft, 1000)

    return () => clearInterval(timer)
  }, [endDate])

  return (
    <div className={className}>
      <div className="flex items-center gap-1 text-xs font-medium">
        {timeLeft.days > 0 && (
          <span className="flex items-center">
            {timeLeft.days}d
          </span>
        )}
        <span className="flex items-center">
          {String(timeLeft.hours).padStart(2, '0')}h
        </span>
        <span className="flex items-center">
          {String(timeLeft.minutes).padStart(2, '0')}m
        </span>
        <span className="flex items-center">
          {String(timeLeft.seconds).padStart(2, '0')}s
        </span>
      </div>
    </div>
  )
} 