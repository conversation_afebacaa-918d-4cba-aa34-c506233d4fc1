import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { GalleryImage } from '@/app/lib/mongodb/schema'
import { ObjectId } from 'mongodb'

interface Tag {
  _id: ObjectId;
  name: string;
}

interface DBImage extends Omit<GalleryImage, '_id' | 'tags'> {
  _id: ObjectId;
  tags: ObjectId[];
}

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    // Public access for gallery items - no authentication required

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const tag = searchParams.get('tag')
    const featured = searchParams.get('featured') === 'true'
    const limit = parseInt(searchParams.get('limit') || '0')

    const db = await getDb()
    let query: any = {}

    // If category is provided, find the category document first
    if (category) {
      const categoryDoc = await db.collection(COLLECTIONS.CATEGORIES)
        .findOne({ name: category })

      if (categoryDoc) {
        query.category = categoryDoc.name
      }
    }

    // If tag is provided, add it to the query
    if (tag) {
      query.tags = new ObjectId(tag)
    }

    // If featured is true, add it to the query
    if (featured) {
      query.isFeatured = true
    }

    // Create the base query
    let dbQuery = db.collection(COLLECTIONS.GALLERY)
      .find(query)
      .sort({ featuredOrder: 1, createdAt: -1 })

    // Apply limit if provided
    if (limit > 0) {
      dbQuery = dbQuery.limit(limit)
    }

    // Execute the query
    const images = await dbQuery.toArray() as DBImage[]
    console.log('Raw images from DB:', images)

    // Get unique tag IDs from all images
    const tagIds = Array.from(new Set(
      images.flatMap(image => (image.tags || []).map((id: string | ObjectId) =>
        id instanceof ObjectId ? id : new ObjectId(id.toString())
      ))
    ))

    console.log('Unique tag IDs:', tagIds)

    // Fetch all tags that are used in the images
    const tags = tagIds.length > 0
      ? await db.collection(COLLECTIONS.TAGS)
          .find({ _id: { $in: tagIds } })
          .toArray() as Tag[]
      : []

    console.log('Found tags:', tags)

    // Create a map of tag IDs to tag documents
    const tagMap = new Map(
      tags.map(tag => [tag._id.toString(), tag])
    )

    // Format the response
    const formattedImages = images.map(image => {
      const formattedImage = {
        ...image,
        _id: image._id.toString(),
        tags: (image.tags || [])
          .map((tagId: string | ObjectId) => {
            const tagIdStr = tagId instanceof ObjectId ? tagId.toString() : tagId.toString()
            const tag = tagMap.get(tagIdStr)
            if (!tag) {
              console.log('Could not find tag for ID:', tagIdStr)
              return null
            }
            return {
              _id: tag._id.toString(),
              name: tag.name
            }
          })
          .filter(Boolean)
      }
      console.log('Formatted image:', formattedImage)
      return formattedImage
    })

    // Create response with CORS headers
    const response = NextResponse.json(formattedImages)
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    return response

  } catch (error) {
    console.error('Error in gallery GET:', error)
    const errorResponse = NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    )
    errorResponse.headers.set('Access-Control-Allow-Origin', '*')
    return errorResponse
  }
}

// Add OPTIONS handler for CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 204 })
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  return response
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]
    const authResult = await verifyAuth(token, true) // true for requiring admin
    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      )
    }

    const db = await getDb()
    const data = await request.json()

    // Validate required fields
    if (!data.imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      )
    }

    if (!data.category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      )
    }

    // Convert tag IDs to ObjectIds
    const tags = data.tags ? data.tags.map((tagId: string) => tagId) : []

    // Verify that the category exists
    const categoryDoc = await db.collection(COLLECTIONS.CATEGORIES)
      .findOne({ name: data.category })

    if (!categoryDoc) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    const newImage = {
      imageUrl: data.imageUrl,
      category: categoryDoc.name, // Store just the category name
      tags: tags, // Store tags as plain strings
      isFeatured: data.isFeatured || false,
      featuredOrder: data.isFeatured ? (data.featuredOrder || 0) : null,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = await db.collection(COLLECTIONS.GALLERY).insertOne(newImage)

    // Format the response
    const responseData = {
      ...newImage,
      _id: result.insertedId.toString(),
      category: categoryDoc.name,
      tags: tags // Send back the tags as plain strings
    }

    return NextResponse.json(responseData, { status: 201 })
  } catch (error) {
    console.error('Error in gallery POST:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    )
  }
}