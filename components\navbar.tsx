"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { Palette } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "./mode-toggle"
import { toast } from "sonner"
import { PromotionBanner } from "./promotion-banner"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { JWTPayload } from "@/lib/auth"

export function Navbar() {
  const pathname = usePathname()
  const router = useRouter()
  const [user, setUser] = React.useState<JWTPayload | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [mobileOpen, setMobileOpen] = React.useState(false)

  // Function to convert MinIO URL to proxy URL
  const getProxyUrl = (url: string) => {
    return `/api/proxy?url=${encodeURIComponent(url)}`
  }

  React.useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(res => res.json())
      .then(data => {
        if (data.user) {
          setUser(data.user)
        } else {
          localStorage.removeItem('token')
        }
      })
      .catch(() => {
        localStorage.removeItem('token')
      })
      .finally(() => {
        setLoading(false)
      })
    } else {
      setLoading(false)
    }
  }, [pathname])

  const handleAdminClick = async () => {
    const token = localStorage.getItem('token')

    if (!token) {
      toast.error('No authentication token found')
      router.push('/login')
      return
    }

    if (!user) {
      toast.error('User session not found')
      router.push('/login')
      return
    }

    if (user.role?.toLowerCase() !== 'admin') {
      toast.error('Admin access required')
      router.push('/')
      return
    }

    document.cookie = `token=${token}; path=/; secure; samesite=lax`
    router.push('/admin')
  }

  const handleLogout = () => {
    localStorage.removeItem('token')
    setUser(null)
    setMobileOpen(false)
    router.push('/login')
    router.refresh()
  }

  const NavigationItems = () => (
    <>
      <NavigationMenuItem>
        <NavigationMenuTrigger>Services</NavigationMenuTrigger>
        <NavigationMenuContent>
          <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
            <li className="row-span-3">
              <NavigationMenuLink asChild>
                <a
                  className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                  href="/services#custom-designs"
                >
                  <Palette className="h-6 w-6" />
                  <div className="mb-2 mt-4 text-lg font-medium">
                    Custom Designs
                  </div>
                  <p className="text-sm leading-tight text-muted-foreground">
                    Unique tattoo designs crafted just for you
                  </p>
                </a>
              </NavigationMenuLink>
            </li>
            <li>
              <NavigationMenuLink asChild>
                <a
                  className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                  href="/services#flash"
                >
                  <div className="text-sm font-medium leading-none">Flash Designs</div>
                  <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                    Pre-made designs ready for immediate tattooing
                  </p>
                </a>
              </NavigationMenuLink>
            </li>
            <li>
              <NavigationMenuLink asChild>
                <a
                  className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                  href="/services#cover-ups"
                >
                  <div className="text-sm font-medium leading-none">Cover-ups</div>
                  <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                    Transform existing tattoos into new artwork
                  </p>
                </a>
              </NavigationMenuLink>
            </li>
          </ul>
        </NavigationMenuContent>
      </NavigationMenuItem>
      <NavigationMenuItem>
        <Link href="/gallery" legacyBehavior passHref>
          <NavigationMenuLink className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50">
            Gallery
          </NavigationMenuLink>
        </Link>
      </NavigationMenuItem>
      <NavigationMenuItem>
        <Link href="/designs" legacyBehavior passHref>
          <NavigationMenuLink className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50">
            Designs
          </NavigationMenuLink>
        </Link>
      </NavigationMenuItem>
      <NavigationMenuItem>
        <Link href="/aftercare" legacyBehavior passHref>
          <NavigationMenuLink className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50">
            Aftercare
          </NavigationMenuLink>
        </Link>
      </NavigationMenuItem>
      <NavigationMenuItem>
        <Link href="/contact" legacyBehavior passHref>
          <NavigationMenuLink className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50">
            Contact
          </NavigationMenuLink>
        </Link>
      </NavigationMenuItem>
    </>
  )

  const MobileMenu = () => (
    <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 xl:hidden"
          size="sm"
        >
          <svg
            strokeWidth="1.5"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
          >
            <path
              d="M3 5H11"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 12H16"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 19H21"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0">
        <ScrollArea className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
          <div className="flex flex-col space-y-3">
            <Link
              href="/"
              className="flex items-center space-x-2"
              onClick={() => setMobileOpen(false)}
            >
              <div className="relative h-8 w-8 overflow-hidden rounded-sm">
                <img src={getProxyUrl("https://minioapi.realsoftgames.com/jimmys-bali-ink/logo.jpeg")} alt="Jimmy's Bali Ink Logo" className="absolute inset-0 w-full h-full object-cover" />
              </div>
              <span className="font-bold">Jimmy&apos;s Bali Ink</span>
            </Link>
            <div className="flex flex-col space-y-2">
              <div className="flex flex-col space-y-3 pt-6">
                <h4 className="text-sm font-medium">Services</h4>
                <div className="grid gap-3">
                  <Link
                    href="/services/custom-designs"
                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                    onClick={() => setMobileOpen(false)}
                  >
                    <Palette className="h-6 w-6" />
                    <div className="mb-2 mt-4 text-lg font-medium">
                      Custom Designs
                    </div>
                    <p className="text-sm leading-tight text-muted-foreground">
                      Unique tattoo designs crafted just for you
                    </p>
                  </Link>
                  <div className="grid gap-2">
                    <Link
                      href="/services#flash"
                      className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                      onClick={() => setMobileOpen(false)}
                    >
                      <div className="text-sm font-medium leading-none">Flash Tattoos</div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Browse our collection of ready-to-ink designs at fixed prices
                      </p>
                    </Link>
                    <Link
                      href="/services#cover-ups"
                      className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                      onClick={() => setMobileOpen(false)}
                    >
                      <div className="text-sm font-medium leading-none">Cover-ups</div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Transform existing tattoos into new artwork
                      </p>
                    </Link>
                  </div>
                </div>
              </div>
              <div className="flex flex-col space-y-2 pt-6">
                <Link
                  href="/gallery"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setMobileOpen(false)}
                >
                  Gallery
                </Link>
                <Link
                  href="/designs"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setMobileOpen(false)}
                >
                  Designs
                </Link>
                <Link
                  href="/aftercare"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setMobileOpen(false)}
                >
                  Aftercare
                </Link>
                <Link
                  href="/contact"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setMobileOpen(false)}
                >
                  Contact
                </Link>
              </div>
              <div className="flex flex-col space-y-2 pt-6">
                {loading ? (
                  <span className="text-muted-foreground">Loading...</span>
                ) : user ? (
                  <>
                    <div className="flex items-center space-x-2">
                      <span className="text-muted-foreground">Welcome,</span>
                      <span className="font-medium">{user.firstName}</span>
                    </div>
                    {user.role?.toLowerCase() === 'admin' && (
                      <Button
                        variant="ghost"
                        className="justify-start"
                        onClick={() => {
                          setMobileOpen(false);
                          handleAdminClick();
                        }}
                      >
                        Admin Dashboard
                      </Button>
                    )}
                    <Button variant="ghost" onClick={handleLogout} className="justify-start">
                      Logout
                    </Button>
                  </>
                ) : (
                  <Button variant="ghost" asChild className="justify-start" onClick={() => setMobileOpen(false)}>
                    <Link href="/login">Login</Link>
                  </Button>
                )}
                <Button asChild className="justify-start" onClick={() => setMobileOpen(false)}>
                  <Link href="/book">Book Now</Link>
                </Button>
              </div>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  )

  return (
    <div className="sticky top-0 z-50 w-full">
      <div className="relative">
        <header className="w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-50">
          <div className="container flex flex-wrap items-center justify-between h-14 px-4 md:px-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="relative h-8 w-8 overflow-hidden rounded-sm">
                  <img src={getProxyUrl("https://minioapi.realsoftgames.com/jimmys-bali-ink/logo.jpeg")} alt="Jimmy's Bali Ink Logo" className="absolute inset-0 w-full h-full object-cover" />
                </div>
                <span className="font-bold whitespace-nowrap">Jimmy&apos;s Bali Ink</span>
              </Link>
              <div className="hidden xl:block ml-4 z-[200]">
                <NavigationMenu className="relative z-[200]">
                  <NavigationMenuList>
                    <NavigationItems />
                  </NavigationMenuList>
                </NavigationMenu>
              </div>
            </div>
            <div className="flex items-center">
              <nav className="flex items-center gap-x-2">
                {loading ? (
                  <span className="text-muted-foreground text-sm">Loading...</span>
                ) : user ? (
                  <>
                    <div className="hidden lg:flex items-center gap-x-2">
                      <span className="text-muted-foreground text-sm">Welcome,</span>
                      <span className="font-medium text-sm truncate max-w-[100px]">{user.firstName}</span>
                      {user.role?.toLowerCase() === 'admin' && (
                        <Button
                          variant="ghost"
                          onClick={handleAdminClick}
                          className="h-9 px-3"
                          size="sm"
                        >
                          Admin Dashboard
                        </Button>
                      )}
                      <Button variant="ghost" onClick={handleLogout} size="sm" className="h-9 px-3">
                        Logout
                      </Button>
                    </div>
                  </>
                ) : (
                  <Button variant="ghost" asChild className="hidden lg:inline-flex h-9 px-3" size="sm">
                    <Link href="/login">Login</Link>
                  </Button>
                )}
                <Button asChild className="hidden lg:inline-flex h-9 px-3 whitespace-nowrap" size="sm">
                  <Link href="/book">Book Now</Link>
                </Button>
                <div className="flex items-center gap-x-2">
                  <ModeToggle />
                  <MobileMenu />
                </div>
              </nav>
            </div>
          </div>
        </header>
        <div className="relative z-10">
          <PromotionBanner />
        </div>
      </div>
    </div>
  )
}
