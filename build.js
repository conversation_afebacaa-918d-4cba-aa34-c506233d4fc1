const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to copy directory recursively
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Clean up previous build artifacts
function cleanBuild() {
  console.log('Cleaning up previous build artifacts...');
  if (fs.existsSync('.next')) {
    try {
      fs.rmSync('.next', { recursive: true, force: true });
    } catch (error) {
      console.warn('Could not remove .next directory:', error.message);
    }
  }
}

// Create necessary directories for the build
function createDirectories() {
  console.log('Creating necessary directories...');
  const dirs = [
    '.next',
    '.next/standalone',
    '.next/static'
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// Run the build process
async function runBuild() {
  try {
    cleanBuild();
    createDirectories();

    console.log('Building Next.js application...');
    execSync('npx next build --no-lint', { stdio: 'inherit' });
    console.log('Build completed successfully!');

    // Check if server.js exists
    if (fs.existsSync('.next/standalone/server.js')) {
      console.log('server.js found in .next/standalone/');
    } else {
      console.error('ERROR: server.js not found in .next/standalone/');
      console.log('Checking for server.js in other locations...');

      // Check if it's in the root .next directory
      if (fs.existsSync('.next/server.js')) {
        console.log('Found server.js in .next/, copying to .next/standalone/');
        fs.copyFileSync('.next/server.js', '.next/standalone/server.js');
      } else {
        console.error('ERROR: server.js not found in any expected location');
      }
    }

    // Copy necessary files
    if (fs.existsSync('public')) {
      copyDir('public', path.join('.next', 'standalone', 'public'));
    }

    // Copy .next/static to .next/standalone/.next/static
    if (fs.existsSync('.next/static')) {
      console.log('Copying .next/static to .next/standalone/.next/static');
      fs.mkdirSync('.next/standalone/.next', { recursive: true });
      copyDir('.next/static', '.next/standalone/.next/static');
    } else {
      console.warn('Warning: .next/static directory not found');
    }

    // Copy other necessary Next.js files
    if (fs.existsSync('.next/BUILD_ID')) {
      console.log('Copying BUILD_ID file');
      fs.copyFileSync('.next/BUILD_ID', '.next/standalone/.next/BUILD_ID');
    }

    // Create a simple script to verify static files
    const verifyScript = `
const fs = require('fs');
const path = require('path');

console.log('Verifying static files...');

if (fs.existsSync(path.join(__dirname, '.next/static'))) {
  console.log('Static files exist at .next/static');
  const files = fs.readdirSync(path.join(__dirname, '.next/static'));
  console.log('Files in .next/static:', files);
} else {
  console.error('ERROR: Static files not found at .next/static');
}
`;

    fs.writeFileSync('.next/standalone/verify-static.js', verifyScript);
    console.log('Created verification script at .next/standalone/verify-static.js');

    console.log('Build process completed successfully.');
  } catch (error) {
    console.error('Build failed:', error.message);
    process.exit(1);
  }
}

// Execute build
runBuild().catch(error => {
  console.error('Unhandled build error:', error);
  process.exit(1);
});

