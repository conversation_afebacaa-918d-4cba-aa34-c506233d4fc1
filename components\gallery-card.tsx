
import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getImageUrl } from "@/app/lib/file-utils"

interface Tag {
  _id: string
  name: string
}

interface GalleryCardProps {
  _id: string
  imageUrl: string
  category: string | { name: string } | null
  tags: Tag[]
  isFeatured?: boolean
  onImageClick: () => void
  onTagClick?: (tagName: string, e: React.MouseEvent) => void
  onCategoryClick?: (categoryName: string, e: React.MouseEvent) => void
}

export function GalleryCard({
  imageUrl,
  category,
  tags,
  isFeatured,
  onImageClick,
  onTagClick,
  onCategoryClick
}: GalleryCardProps) {
  const [imageError, setImageError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  const categoryName = category
    ? typeof category === 'string'
      ? category
      : category.name
    : 'Uncategorized'

  // Use our image utility function to get the correct URL with options
  const imageUrlToUse = getImageUrl(imageUrl, {
    cacheBust: retryCount > 0, // Add cache busting if we've retried
    bypassCache: retryCount > 1 // Bypass cache on second retry
  })

  // Handle image loading error
  const handleImageError = () => {
    console.error(`Error loading gallery image: ${imageUrl}`);

    // Only retry up to 2 times
    if (retryCount < 2) {
      setRetryCount(retryCount + 1);
    } else {
      setImageError(true);
    }
  };

  return (
    <Card className="overflow-hidden group hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
      <CardContent className="p-0">
        <div
          className="aspect-square relative cursor-pointer"
          onClick={onImageClick}
        >
          {isFeatured && (
            <Badge
              variant="default"
              className="absolute top-2 right-2 z-20 bg-black/80 hover:bg-black/90 text-white border border-white/20 text-xs font-medium"
            >
              Featured
            </Badge>
          )}
          <div className="relative w-full h-full overflow-hidden">
            {!imageError ? (
              <img
                src={imageUrlToUse}
                alt={`${categoryName} tattoo design`}
                className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                onError={handleImageError}
              />
            ) : (
              <div className="absolute inset-0 w-full h-full flex items-center justify-center bg-gray-200">
                <span className="text-gray-500 text-sm">Image unavailable</span>
              </div>
            )}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 pointer-events-none"></div>
          </div>

          {/* Center "Click to view" text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-white text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black/40 px-4 py-2 rounded-full transform scale-90 group-hover:scale-100 transition-transform">
              Click to view
            </span>
          </div>

          {/* Always visible overlay with category and tags */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent">
            <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2">
              <div>
                <p
                  className="text-lg font-semibold capitalize text-white cursor-pointer hover:text-white/80"
                  onClick={(e) => {
                    e.stopPropagation()
                    onCategoryClick?.(categoryName, e)
                  }}
                >
                  {categoryName}
                </p>
              </div>
              {tags && tags.length > 0 && (
                <div>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <Badge
                        key={tag._id}
                        variant="outline"
                        className="text-xs px-2.5 py-1 bg-black/60 text-white border-white/20 cursor-pointer hover:bg-black/80"
                        onClick={(e) => {
                          e.stopPropagation()
                          onTagClick?.(tag.name, e)
                        }}
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}