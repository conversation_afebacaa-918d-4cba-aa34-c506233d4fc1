"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { format } from "date-fns"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AppointmentCalendar } from "@/components/appointment-calendar"
import { SelectWithDescription } from "@/components/ui/select-with-description"
import { ArrowLeft } from "lucide-react"

interface Category {
  _id: string
  name: string
  description?: string
  slug: string
}

interface Size {
  _id: string
  name: string
  description?: string
}

export default function NewAppointmentPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [sizes, setSizes] = useState<Size[]>([])
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    serviceType: "",
    size: "",
    description: "",
    date: "",
    time: undefined as string | undefined,
    status: "pending" as const
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories')
        if (!categoriesResponse.ok) throw new Error('Failed to fetch categories')
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData)

        // Fetch sizes
        const sizesResponse = await fetch('/api/sizes')
        if (!sizesResponse.ok) throw new Error('Failed to fetch sizes')
        const sizesData = await sizesResponse.json()
        setSizes(sizesData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error("Failed to load form data")
      }
    }

    fetchData()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const selectedCategory = categories.find(cat => cat._id === formData.serviceType)
      if (!selectedCategory) {
        throw new Error("Please select a valid service type")
      }

      const selectedSize = sizes.find(size => size._id === formData.size)
      if (!selectedSize) {
        throw new Error("Please select a valid size")
      }

      const response = await fetch('/api/admin/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          ...formData,
          serviceType: {
            _id: selectedCategory._id,
            name: selectedCategory.name,
            slug: selectedCategory.slug
          },
          size: {
            _id: selectedSize._id,
            name: selectedSize.name,
            description: selectedSize.description
          }
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Failed to create appointment")
      }

      toast.success("Appointment created successfully")
      router.push("/admin/appointments")
    } catch (error) {
      console.error("Error creating appointment:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create appointment")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-16 px-4">
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="mb-8"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Appointments
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>Create New Appointment</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name</Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Service Type</Label>
                <SelectWithDescription
                  items={categories}
                  value={formData.serviceType}
                  onValueChange={(value) => setFormData({ ...formData, serviceType: value })}
                  placeholder="Select service type"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Size</Label>
              <SelectWithDescription
                items={sizes}
                value={formData.size}
                onValueChange={(value) => setFormData({ ...formData, size: value })}
                placeholder="Select size"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label>Appointment Date & Time</Label>
              <AppointmentCalendar
                selectedDate={formData.date ? new Date(formData.date) : undefined}
                selectedTime={formData.time}
                onDateChange={(date) => date && setFormData({ 
                  ...formData, 
                  date: date.toISOString(),
                  time: undefined 
                })}
                onTimeChange={(time) => setFormData({ ...formData, time })}
              />
            </div>

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Creating..." : "Create Appointment"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 