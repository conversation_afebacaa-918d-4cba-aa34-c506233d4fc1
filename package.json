{"name": "jimmy<PERSON><PERSON><PERSON>nk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "node build.js", "start": "node scripts/create-minio-bucket.js && NODE_ENV=production node .next/standalone/server.js", "verify-static": "node .next/standalone/verify-static.js", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.4", "@iconify/react": "^5.2.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@types/js-cookie": "^3.0.6", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.6.2", "input-otp": "^1.4.2", "jose": "^6.0.10", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.323.0", "mongodb": "^6.15.0", "next": "^14.2.28", "next-themes": "^0.2.1", "nodemailer": "^6.10.0", "react": "^18", "react-day-picker": "^9.6.4", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-hook-form": "^7.50.1", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "resend": "^4.2.0", "sharp": "^0.33.5", "sonner": "^1.4.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "vaul": "^1.1.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5"}}