import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Pencil, Trash2 } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { formatIDR } from '@/lib/utils'
import { getImageUrl } from '@/app/lib/file-utils'

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  } | string
  category: {
    _id: string
    name: string
  } | string
  tags: {
    _id: string
    name: string
  }[]
  media: {
    url: string
    isPrimary: boolean
  }[]
  status: 'draft' | 'live'
}

interface Props {
  design: Design
  onStatusChange: (id: string, status: 'draft' | 'live') => void
  onDelete: (id: string) => void
  getSizeName: (sizeId: string) => string
  getCategoryName: (category: Design['category']) => string
}

export function AdminDesignCard({
  design,
  onStatusChange,
  onDelete,
  getSizeName,
  getCategoryName,
}: Props) {
  const router = useRouter()

  return (
    <Card className="overflow-hidden h-full flex flex-col bg-black text-white">
      <CardContent className="p-0 flex-1 flex flex-col">
        <div className="aspect-square relative w-full">
          <Image
            src={getImageUrl(design.media[0].url)}
            alt={design.title}
            fill
            className="object-cover"
            unoptimized={design.media[0].url?.includes('minioapi.realsoftgames.com') || design.media[0].url?.includes('minio.realsoftgames.com')}
          />

          {/* Dark overlay with category and tags */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent">
            <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2">
              <div className="space-y-1">
                <p className="text-lg font-semibold capitalize text-white">
                  {getCategoryName(design.category)}
                </p>
              </div>

              {/* Tags */}
              {design.tags && design.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {design.tags.map(tag => (
                    <Badge
                      key={tag._id}
                      variant="outline"
                      className="text-xs px-2.5 py-1 bg-black/60 text-white border-white/20 hover:bg-black/80"
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="p-4 flex-1 flex flex-col space-y-2">
          <h3 className="text-xl font-semibold">{design.title}</h3>

          <div className="flex items-center gap-2">
            <span className="text-gray-400">Price:</span>
            <span className="text-xl font-semibold">{formatIDR(design.price)}</span>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-gray-400">Size:</span>
            <span>
              {typeof design.size === 'object' && design.size !== null
                ? design.size.name
                : getSizeName(design.size as string)}
            </span>
          </div>

          <div className="flex items-center justify-between pt-4 mt-2 border-t border-zinc-800">
            <div className="flex items-center gap-2">
              <Switch
                checked={design.status === 'live'}
                onCheckedChange={(checked) =>
                  onStatusChange(design._id, checked ? 'live' : 'draft')
                }
              />
              <span>
                {design.status === 'live' ? 'Live' : 'Draft'}
              </span>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={() => router.push(`/admin/designs/${design._id}`)}
                className="bg-zinc-800 hover:bg-zinc-700"
              >
                <Pencil className="h-4 w-4 mr-1" />
                Edit
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => onDelete(design._id)}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}