const { S3Client, HeadBucketCommand, CreateBucketCommand, PutBucketPolicyCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');

// Initialize S3 client with MinIO configuration
const s3Client = new S3Client({
  region: process.env.S3_REGION || 'us-east-1',
  endpoint: process.env.S3_ENDPOINT,
  credentials: {
    accessKeyId: process.env.S3_ACCESS_KEY || '',
    secretAccessKey: process.env.S3_SECRET_KEY || '',
  },
  forcePathStyle: process.env.S3_PATH_STYLE === 'true',
});

const bucketName = process.env.S3_BUCKET || 'jimmys-bali-ink';

// Check if bucket exists
async function checkBucketExists() {
  try {
    console.log(`Checking if bucket '${bucketName}' exists...`);
    const command = new HeadBucketCommand({ Bucket: bucketName });
    await s3Client.send(command);
    console.log(`Bucket '${bucketName}' already exists.`);
    return true;
  } catch (error) {
    if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
      console.log(`Bucket '${bucketName}' does not exist.`);
      return false;
    }
    console.error('Error checking bucket existence:', error);
    // If there's an error other than "not found", we'll assume the bucket might exist
    // but we don't have permission to check, or there's a connection issue
    return null;
  }
}

// Create bucket if it doesn't exist
async function createBucket() {
  try {
    console.log(`Creating bucket '${bucketName}'...`);
    const command = new CreateBucketCommand({ Bucket: bucketName });
    await s3Client.send(command);
    console.log(`Bucket '${bucketName}' created successfully.`);
    return true;
  } catch (error) {
    console.error('Error creating bucket:', error);
    return false;
  }
}

// Set public read policy for the bucket
async function setBucketPolicy() {
  try {
    console.log(`Setting public read policy for bucket '${bucketName}'...`);

    // Policy that allows public read access to all objects in the bucket
    const policy = {
      Version: '2012-10-17',
      Statement: [
        {
          Effect: 'Allow',
          Principal: '*',
          Action: ['s3:GetObject'],
          Resource: [`arn:aws:s3:::${bucketName}/*`]
        }
      ]
    };

    const command = new PutBucketPolicyCommand({
      Bucket: bucketName,
      Policy: JSON.stringify(policy)
    });

    await s3Client.send(command);
    console.log(`Public read policy set for bucket '${bucketName}'.`);
    return true;
  } catch (error) {
    console.error('Error setting bucket policy:', error);
    return false;
  }
}

// Verify static files
function verifyStaticFiles() {
  console.log('\nVerifying static files...');

  const staticPath = path.join(process.cwd(), '.next/static');
  if (fs.existsSync(staticPath)) {
    console.log('✅ Static files exist at:', staticPath);
    try {
      const files = fs.readdirSync(staticPath);
      console.log(`Found ${files.length} directories in .next/static:`, files);

      // Check for CSS and JS files
      let hasCSS = false;
      let hasJS = false;

      for (const dir of files) {
        const dirPath = path.join(staticPath, dir);
        if (fs.statSync(dirPath).isDirectory()) {
          const dirFiles = fs.readdirSync(dirPath);
          console.log(`- ${dir} directory contains ${dirFiles.length} files`);

          for (const file of dirFiles) {
            if (file.endsWith('.css')) hasCSS = true;
            if (file.endsWith('.js')) hasJS = true;
          }
        }
      }

      console.log('CSS files found:', hasCSS ? '✅ Yes' : '❌ No');
      console.log('JS files found:', hasJS ? '✅ Yes' : '❌ No');

    } catch (error) {
      console.error('Error reading static directory:', error);
    }
  } else {
    console.error('❌ ERROR: Static files not found at:', staticPath);
    console.log('Current directory:', process.cwd());
    console.log('Directory contents:', fs.readdirSync(process.cwd()));

    // Check if .next exists
    const nextDir = path.join(process.cwd(), '.next');
    if (fs.existsSync(nextDir)) {
      console.log('.next directory exists, contents:', fs.readdirSync(nextDir));
    } else {
      console.error('❌ ERROR: .next directory not found');
    }
  }
}

// Main function
async function main() {
  try {
    console.log('Starting MinIO bucket setup and static file verification...');

    // Check if required environment variables are set
    if (!process.env.S3_ENDPOINT || !process.env.S3_ACCESS_KEY || !process.env.S3_SECRET_KEY) {
      console.warn('MinIO environment variables not set. Skipping bucket creation.');
    } else {

    // Check if bucket exists
    const exists = await checkBucketExists();

    // If bucket doesn't exist, create it
    if (exists === false) {
      const created = await createBucket();
      if (!created) {
        console.error('Failed to create bucket. Exiting.');
        return;
      }
    } else if (exists === null) {
      console.warn('Could not determine if bucket exists. Skipping bucket creation.');
      return;
    }

    // Set bucket policy
    await setBucketPolicy();

    console.log('Bucket setup completed successfully.');
    }

    // Always verify static files, even if MinIO setup is skipped
    verifyStaticFiles();

    console.log('\nSetup complete. Starting server...');
  } catch (error) {
    console.error('Unexpected error during setup:', error);
    console.log('Continuing despite error - the application will still start.');
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  // Don't exit the process, as this would prevent the server from starting
  console.log('Continuing despite fatal error - the application will still start.');
});
