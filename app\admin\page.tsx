"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { PlusCircle, Users, Image, Tag, CalendarRange, Tag as TagIcon, Layers, ListFilter, Box } from "lucide-react"
import { toast } from "sonner"

interface Stats {
  designs: number
  gallery: number
  sizes: number
  categories: number
  tags: number
  users: number
  appointments: number
  promotions: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    designs: 0,
    gallery: 0,
    sizes: 0,
    categories: 0,
    tags: 0,
    users: 0,
    appointments: 0,
    promotions: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await fetch('/api/admin/stats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('Failed to fetch stats')
        }

        const data = await response.json()
        setStats(data)
      } catch (error) {
        console.error('Error fetching stats:', error)
        toast.error('Failed to fetch dashboard stats')
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  return (
    <div className="flex-1 space-y-4 p-4 sm:p-6 md:p-8 pt-4 sm:pt-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
        <h2 className="text-3xl font-bold tracking-tight">Admin Dashboard</h2>
        <div className="flex flex-col sm:flex-row gap-2">
          <Link href="/admin/designs/new" className="w-full sm:w-auto">
            <Button className="w-full sm:w-auto">
              <PlusCircle className="mr-2 h-4 w-4" />
              <span className="whitespace-nowrap">Add Design</span>
            </Button>
          </Link>
          <Link href="/admin/gallery/new" className="w-full sm:w-auto">
            <Button className="w-full sm:w-auto">
              <PlusCircle className="mr-2 h-4 w-4" />
              <span className="whitespace-nowrap">Add Gallery</span>
            </Button>
          </Link>
          <Link href="/admin/promotions/new" className="w-full sm:w-auto">
            <Button className="w-full sm:w-auto">
              <PlusCircle className="mr-2 h-4 w-4" />
              <span className="whitespace-nowrap">Add Promotion</span>
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Designs */}
        <Link href="/admin/designs">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Designs</h3>
                <p className="text-sm text-muted-foreground">Manage flash and custom designs</p>
              </div>
              <Layers className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.designs} designs available`}
            </p>
          </div>
        </Link>

        {/* Gallery */}
        <Link href="/admin/gallery">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Gallery</h3>
                <p className="text-sm text-muted-foreground">Manage gallery images</p>
              </div>
              <Image className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.gallery} images in gallery`}
            </p>
          </div>
        </Link>

        {/* Sizes */}
        <Link href="/admin/sizes">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Sizes</h3>
                <p className="text-sm text-muted-foreground">Manage design sizes</p>
              </div>
              <Box className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.sizes} sizes available`}
            </p>
          </div>
        </Link>

        {/* Categories */}
        <Link href="/admin/categories">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Categories</h3>
                <p className="text-sm text-muted-foreground">Manage design categories</p>
              </div>
              <ListFilter className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.categories} categories available`}
            </p>
          </div>
        </Link>

        {/* Tags */}
        <Link href="/admin/tags">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Tags</h3>
                <p className="text-sm text-muted-foreground">Manage design tags</p>
              </div>
              <TagIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.tags} tags available`}
            </p>
          </div>
        </Link>

        {/* Users */}
        <Link href="/admin/users">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Users</h3>
                <p className="text-sm text-muted-foreground">Manage user accounts</p>
              </div>
              <Users className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.users} users registered`}
            </p>
          </div>
        </Link>

        {/* Appointments */}
        <Link href="/admin/appointments">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Appointments</h3>
                <p className="text-sm text-muted-foreground">Manage tattoo appointments</p>
              </div>
              <CalendarRange className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.appointments} appointments`}
            </p>
          </div>
        </Link>

        {/* Special Offers */}
        <Link href="/admin/promotions">
          <div className="rounded-lg border p-6 hover:bg-accent transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold">Special Offers</h3>
                <p className="text-sm text-muted-foreground">Manage promotional offers</p>
              </div>
              <Tag className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="mt-4 text-2xl font-bold">
              {loading ? "Loading..." : `${stats.promotions} active offer`}
            </p>
          </div>
        </Link>
      </div>
    </div>
  )
}