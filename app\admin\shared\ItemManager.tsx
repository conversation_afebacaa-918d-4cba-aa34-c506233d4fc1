"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { DndProvider, useDrag, useDrop, useDragLayer } from 'react-dnd'
import { getEmptyImage } from 'react-dnd-html5-backend'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { Button } from "@/components/ui/button"
import { Loader2, GripVertical, Trash2, Pencil } from "lucide-react"
import { BaseItem } from "@/types"

interface ItemManagerProps<T extends BaseItem> {
  title: string
  description?: string
  items: T[]
  loading: boolean
  onAddClick: () => void
  onEditClick: (item: T) => void
  onDeleteClick: (id: string) => void
  onReorder?: (items: T[]) => void
  sortable?: boolean
}

interface DraggableItemProps<T extends BaseItem> {
  item: T
  index: number
  moveItem: (dragIndex: number, hoverIndex: number) => void
  onEdit: (item: T) => void
  onDelete: (id: string) => void
}

// Custom drag layer component
const CustomDragLayer = () => {
  const { isDragging, item, currentOffset } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging(),
  }))

  if (!isDragging || !currentOffset) {
    return null
  }

  return (
    <div
      style={{
        position: 'fixed',
        pointerEvents: 'none',
        zIndex: 100,
        left: 0,
        top: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <div
        style={{
          transform: `translate(${currentOffset.x}px, ${currentOffset.y}px)`,
          WebkitTransform: `translate(${currentOffset.x}px, ${currentOffset.y}px)`
        }}
      >
        <div className="rounded-lg border p-4 bg-background/90 shadow-lg w-[calc(100vw-4rem)] max-w-3xl">
          <div className="flex items-center gap-3">
            <GripVertical className="h-5 w-5 text-muted-foreground" />
            <div>
              <h3 className="font-medium">{item.content?.name || 'Item'}</h3>
              {item.content?.description && (
                <p className="text-sm text-muted-foreground">
                  {item.content.description}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface DragItem {
  index: number
  id: string
  type: string
}

const DraggableItem = <T extends BaseItem>({
  item,
  index,
  moveItem,
  onEdit,
  onDelete
}: DraggableItemProps<T>) => {
  const ref = useRef<HTMLDivElement>(null)

  const [{ handlerId }, drop] = useDrop<
    DragItem,
    void,
    { handlerId: string | symbol | null }
  >({
    accept: 'ITEM',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      }
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

      // Determine mouse position
      const clientOffset = monitor.getClientOffset()

      // Get pixels to the top
      const hoverClientY = clientOffset!.y - hoverBoundingRect.top

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      // Time to actually perform the action
      moveItem(dragIndex, hoverIndex)

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
  })

  const [{ isDragging }, drag, preview] = useDrag({
    type: 'ITEM',
    item: () => ({ type: 'ITEM', id: item._id, index }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true })
  }, [preview])

  const opacity = isDragging ? 0 : 1
  drag(drop(ref))

  return (
    <div
      ref={ref}
      style={{ opacity }}
      data-handler-id={handlerId}
      className="mb-2 transition-all duration-200"
    >
      <div
        className={`flex items-center justify-between rounded-lg border p-4 bg-background`}
      >
        <div className="flex items-center gap-3">
          <div className="cursor-grab active:cursor-grabbing touch-none">
            <GripVertical className="h-5 w-5 text-muted-foreground" />
          </div>
          <div>
            <h3 className="font-medium leading-none">{item.name}</h3>
            {item.description && (
              <p className="text-sm text-muted-foreground mt-1.5 line-clamp-2">
                {item.description}
              </p>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => onEdit(item)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="icon"
            onClick={() => onDelete(item._id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export function ItemManager<T extends BaseItem>({
  title,
  description,
  items,
  loading,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onReorder,
  sortable = false
}: ItemManagerProps<T>) {
  const moveItem = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      if (!onReorder) return
      const dragItem = items[dragIndex]
      const newItems = [...items]
      newItems.splice(dragIndex, 1)
      newItems.splice(hoverIndex, 0, dragItem)
      onReorder(newItems)
    },
    [items, onReorder]
  )

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-6">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
        <Button className="w-full sm:w-auto" onClick={onAddClick}>
          <span className="whitespace-nowrap">Add {title.slice(0, -1)}</span>
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="animate-spin h-8 w-8 text-muted-foreground" />
        </div>
      ) : items.length > 0 ? (
        <DndProvider backend={HTML5Backend}>
          <div>
            {items.map((item, index) => (
              <DraggableItem
                key={item._id}
                item={item}
                index={index}
                moveItem={moveItem}
                onEdit={onEditClick}
                onDelete={onDeleteClick}
              />
            ))}
          </div>
          <CustomDragLayer />
        </DndProvider>
      ) : (
        <div className="text-center py-12 text-muted-foreground">
          No {title.toLowerCase()} available
        </div>
      )}
    </div>
  )
}