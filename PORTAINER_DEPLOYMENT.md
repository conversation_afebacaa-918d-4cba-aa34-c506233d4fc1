# Deploying with Portainer

This document provides instructions for deploying the Jimmy's Bali Ink application using Portainer.

## Environment Variables

Since we're no longer using the `.env` file in the docker-compose.yml, you'll need to configure the environment variables directly in Portainer when deploying the stack.

### Required Environment Variables

Add the following environment variables in the Portainer UI when deploying:

```
NODE_ENV=production

# MinIO Configuration
S3_ENDPOINT=http://192.168.0.59:9000
S3_PUBLIC_URL=https://minioapi.realsoftgames.com
S3_ACCESS_KEY=N3S84xv2d6SkXyEY5Yo5
S3_SECRET_KEY=UKRst5kp2msbdzE1txz3eXK162Lc2r62Ni5Sm2GQ
S3_BUCKET=jimmys-bali-ink
S3_REGION=us-east-1
S3_PATH_STYLE=true

# MongoDB Configuration
MONGODB_URI=mongodb://your-mongodb-uri

# Email Configuration
GMAIL_USER=your-gmail-user
GMAIL_APP_PASSWORD=your-gmail-app-password

# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-app-url
JWT_SECRET=your-jwt-secret
```

Replace the placeholder values with your actual configuration values.

## Deployment Steps

1. Log in to Portainer
2. Navigate to Stacks
3. Click "Add stack"
4. Enter a name for the stack (e.g., "jimmys-bali-ink")
5. Upload the docker-compose.yml file or paste its contents
6. In the "Environment variables" section, add all the required environment variables listed above
7. Click "Deploy the stack"

## Persistent Storage

Since we're now using MinIO for all file storage, we no longer need a persistent volume for the application. All files are stored in the MinIO bucket, and all data is stored in MongoDB.

## Updating the Application

To update the application:

1. Build a new Docker image
2. Push it to the registry
3. In Portainer, go to the stack
4. Click "Editor"
5. Click "Pull and redeploy"

This will pull the latest image and redeploy the application without changing any of the environment variables.
