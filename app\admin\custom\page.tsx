"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { toast } from 'sonner'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Pencil, Trash2, Loader2 } from 'lucide-react'
import { formatIDR } from '@/lib/utils'
import { getImageUrl } from '@/app/lib/file-utils'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  }
  internalCategory: string
  categories: {
    _id: string
    name: string
  }[]
  media: {
    url: string
    isPrimary: boolean
  }[]
  status: 'draft' | 'live'
  createdAt: string
}

export default function CustomDesignsPage() {
  const router = useRouter()
  const [designs, setDesigns] = useState<Design[]>([])
  const [loading, setLoading] = useState(true)
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null)

  useEffect(() => {
    fetchDesigns()
  }, [])

  const fetchDesigns = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/designs', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch designs')
      }

      const data = await response.json()
      // Filter for custom designs only
      const customDesigns = data.filter((design: Design) => design.internalCategory === 'custom')
      setDesigns(customDesigns)
    } catch (error) {
      console.error('Error fetching designs:', error)
      toast.error('Failed to fetch designs')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/designs?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to delete design')
      }

      setDesigns(prev => prev.filter(design => design._id !== id))
      toast.success('Design deleted successfully')
    } catch (error) {
      console.error('Error deleting design:', error)
      toast.error('Failed to delete design')
    } finally {
      setDeleteId(null)
    }
  }

  const handleStatusChange = async (id: string, newStatus: 'draft' | 'live') => {
    try {
      setUpdatingStatus(id)
      const token = localStorage.getItem('token')
      const design = designs.find(d => d._id === id)

      if (!design) return

      const response = await fetch(`/api/designs?id=${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...design,
          status: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update design status')
      }

      setDesigns(prev => prev.map(d =>
        d._id === id ? { ...d, status: newStatus } : d
      ))
      toast.success('Status updated successfully')
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error('Failed to update status')
    } finally {
      setUpdatingStatus(null)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Custom Designs</h1>
        <Button
          onClick={() => router.push('/admin/designs/new')}
          className="bg-primary text-primary-foreground hover:bg-primary/90"
        >
          Add Custom Design
        </Button>
      </div>

      {designs.length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {designs.map(design => (
            <Card key={design._id} className="overflow-hidden">
              <CardContent className="p-2">
                {design.media && design.media.length > 0 && (
                  <div className="relative w-full aspect-square mb-2">
                    <Image
                      src={getImageUrl(design.media.find(m => m.isPrimary)?.url || design.media[0].url)}
                      alt={design.title}
                      fill
                      className="object-cover rounded-lg"
                      unoptimized={(design.media.find(m => m.isPrimary)?.url || design.media[0].url)?.includes('minioapi.realsoftgames.com') || (design.media.find(m => m.isPrimary)?.url || design.media[0].url)?.includes('minio.realsoftgames.com')}
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <h3 className="font-semibold text-base truncate">{design.title}</h3>
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{formatIDR(design.price)}</span>
                    <span className="text-muted-foreground">{design.size.name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {design.categories.map(cat => (
                      <span
                        key={cat._id}
                        className="bg-secondary text-secondary-foreground px-2 py-0.5 rounded-full text-xs"
                      >
                        {cat.name}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between mt-2 pt-2 border-t">
                  <div className="flex items-center space-x-1">
                    <Switch
                      checked={design.status === 'live'}
                      disabled={updatingStatus === design._id}
                      onCheckedChange={(checked: boolean) =>
                        handleStatusChange(design._id, checked ? 'live' : 'draft')
                      }
                      className="scale-75"
                    />
                    <span className="text-xs font-medium">
                      {updatingStatus === design._id ? '...' : design.status === 'live' ? 'Live' : 'Draft'}
                    </span>
                  </div>

                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-7 w-7"
                      onClick={() => router.push(`/admin/designs/${design._id}`)}
                    >
                      <Pencil className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="icon"
                      className="h-7 w-7"
                      onClick={() => setDeleteId(design._id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">No custom designs found</p>
          <Button
            onClick={() => router.push('/admin/designs/new')}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Add Your First Custom Design
          </Button>
        </div>
      )}

      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the design.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => deleteId && handleDelete(deleteId)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}