"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { STUDIO_INFO } from "@/lib/config"

export default function PrivacyPolicyPage() {
  return (
    <div className="container mx-auto py-16 px-4">
      <div className="mb-8">
        <Button
          variant="ghost"
          asChild
          className="mb-4"
        >
          <Link href="/">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
        </Button>

        <h1 className="text-3xl font-bold">Privacy Policy</h1>
        <p className="text-muted-foreground mt-2">
          Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Introduction</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            At {STUDIO_INFO.name}, we respect your privacy and are committed to protecting your personal data. 
            This privacy policy will inform you about how we look after your personal data when you visit our website 
            and tell you about your privacy rights and how the law protects you.
          </p>
          <p>
            This privacy policy applies to all information collected through our website, 
            as well as any related services, sales, marketing, or events.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Information We Collect</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            We collect personal information that you voluntarily provide to us when you:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Register on our website</li>
            <li>Book an appointment</li>
            <li>Contact us</li>
            <li>Subscribe to our newsletter</li>
            <li>Participate in promotions or surveys</li>
          </ul>
          <p>
            The personal information we collect may include:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Name, email address, phone number</li>
            <li>Booking preferences and appointment details</li>
            <li>Images you upload as reference for tattoo designs</li>
            <li>Payment information (processed securely through our payment processors)</li>
            <li>Communication preferences</li>
          </ul>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>How We Use Your Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            We use the information we collect for various purposes, including:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Processing and managing your appointments</li>
            <li>Communicating with you about your appointments</li>
            <li>Providing customer support</li>
            <li>Sending you marketing communications (if you've opted in)</li>
            <li>Improving our website and services</li>
            <li>Complying with legal obligations</li>
          </ul>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Information Sharing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            We may share your information with:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Our staff and artists who need access to provide services to you</li>
            <li>Service providers who help us operate our business (e.g., payment processors, email service providers)</li>
            <li>Legal authorities when required by law</li>
          </ul>
          <p>
            We do not sell or rent your personal information to third parties.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Data Security</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            We implement appropriate security measures to protect your personal information. 
            However, no method of transmission over the Internet or electronic storage is 100% secure, 
            so we cannot guarantee absolute security.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Your Rights</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            Depending on your location, you may have certain rights regarding your personal information, including:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>The right to access your personal information</li>
            <li>The right to correct inaccurate information</li>
            <li>The right to request deletion of your information</li>
            <li>The right to restrict or object to processing</li>
            <li>The right to data portability</li>
            <li>The right to withdraw consent</li>
          </ul>
          <p>
            To exercise these rights, please contact us using the information provided below.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Cookies and Tracking Technologies</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            We use cookies and similar tracking technologies to collect and use information about you and your 
            interaction with our website. We use these technologies to improve your experience, analyze website usage, 
            and for marketing purposes.
          </p>
          <p>
            You can set your browser to refuse all or some browser cookies, or to alert you when websites set or access cookies. 
            If you disable or refuse cookies, please note that some parts of this website may become inaccessible or not function properly.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Children's Privacy</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            Our website is not intended for children under 18 years of age. We do not knowingly collect personal 
            information from children under 18. If you are a parent or guardian and believe your child has provided 
            us with personal information, please contact us.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Changes to This Privacy Policy</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            We may update this privacy policy from time to time. The updated version will be indicated by an updated 
            "Last updated" date at the top of this page. We encourage you to review this privacy policy frequently to 
            be informed of how we are protecting your information.
          </p>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Contact Us</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            If you have questions or comments about this privacy policy or our privacy practices, please contact us at:
          </p>
          <div className="mt-4">
            <p className="font-medium">{STUDIO_INFO.name}</p>
            <p>{STUDIO_INFO.getFullAddress()}</p>
            <p>Email: <a href={`mailto:${STUDIO_INFO.email}`} className="text-primary hover:underline">{STUDIO_INFO.email}</a></p>
            <p>Phone: <a href={`tel:${STUDIO_INFO.phone}`} className="text-primary hover:underline">{STUDIO_INFO.phone}</a></p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
