// app/register/page.tsx
"use client";

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"

export default function RegisterPage() {
  const [firstName, setFirstName] = useState("")
  const [surname, setSurname] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [dateOfBirth, setDateOfBirth] = useState("")
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({
    firstName: "",
    surname: "",
    email: "",
    password: "",
    confirmPassword: "",
    dateOfBirth: ""
  })
  const router = useRouter()

  const validateForm = () => {
    const newErrors = {
      firstName: "",
      surname: "",
      email: "",
      password: "",
      confirmPassword: "",
      dateOfBirth: ""
    }
    let isValid = true

    if (!firstName.trim()) {
      newErrors.firstName = "First name is required"
      isValid = false
    }

    if (!surname.trim()) {
      newErrors.surname = "Surname is required"
      isValid = false
    }

    if (!email) {
      newErrors.email = "Email is required"
      isValid = false
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address"
      isValid = false
    }

    if (!dateOfBirth) {
      newErrors.dateOfBirth = "Date of birth is required"
      isValid = false
    }

    if (!password) {
      newErrors.password = "Password is required"
      isValid = false
    } else if (password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long"
      isValid = false
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password"
      isValid = false
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match"
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  // Check if email exists before form submission
  const checkEmailExists = async (email: string) => {
    try {
      const response = await fetch(`/api/auth/check-email?email=${encodeURIComponent(email)}`)
      const data = await response.json()
      return data.exists
    } catch (error) {
      console.error('Error checking email:', error)
      return false
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Validation Error", {
        description: "Please check the form for errors and try again."
      })
      return
    }

    setLoading(true)
    const toastId = toast.loading("Creating your account...")

    try {
      // Check if email exists first
      const emailExists = await checkEmailExists(email)
      if (emailExists) {
        setErrors(prev => ({
          ...prev,
          email: "This email is already registered"
        }))
        toast.error("Email already registered", {
          id: toastId,
          description: (
            <div className="space-y-2">
              <p>This email address is already registered.</p>
              <p>
                Please{" "}
                <Link href="/login" className="underline font-medium">
                  log in here
                </Link>{" "}
                instead.
              </p>
            </div>
          )
        })
        setLoading(false)
        return
      }

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          firstName, 
          surname, 
          email, 
          password, 
          dateOfBirth 
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        const errorMessage = data.error || "An unexpected error occurred. Please try again."
        toast.error("Registration failed", {
          id: toastId,
          description: errorMessage
        })
        setLoading(false)
        return
      }

      // Store token and user data
      localStorage.setItem('token', data.token)
      localStorage.setItem('user', JSON.stringify(data.user))

      // Show success message
      toast.success("Account created successfully!", {
        id: toastId,
        description: "Welcome to Jimmy's Bali Ink!",
        duration: 3000
      })

      // Wait for 1 second to show the success message
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Redirect to home page and refresh to update the UI
      router.push("/")
      router.refresh()
    } catch (error) {
      console.error('Registration error:', error)
      toast.error("Registration failed", {
        id: toastId,
        description: "An unexpected error occurred. Please try again later."
      })
      setLoading(false)
    }
  }

  const clearError = (field: keyof typeof errors) => {
    setErrors(prev => ({ ...prev, [field]: "" }))
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="mx-auto w-full max-w-md space-y-6 p-6">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Create an Account</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Enter your details below to create your account
          </p>
        </div>
        <form onSubmit={handleRegister} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                placeholder="John"
                required
                value={firstName}
                onChange={(e) => {
                  setFirstName(e.target.value)
                  clearError("firstName")
                }}
                disabled={loading}
                autoComplete="given-name"
                className={errors.firstName ? "border-red-500" : ""}
              />
              {errors.firstName && (
                <p className="text-sm text-red-500">{errors.firstName}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="surname">Surname</Label>
              <Input
                id="surname"
                placeholder="Doe"
                required
                value={surname}
                onChange={(e) => {
                  setSurname(e.target.value)
                  clearError("surname")
                }}
                disabled={loading}
                autoComplete="family-name"
                className={errors.surname ? "border-red-500" : ""}
              />
              {errors.surname && (
                <p className="text-sm text-red-500">{errors.surname}</p>
              )}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              required
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value)
                clearError("email")
              }}
              disabled={loading}
              autoComplete="email"
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="dateOfBirth">Date of Birth</Label>
            <Input
              id="dateOfBirth"
              type="date"
              required
              value={dateOfBirth}
              onChange={(e) => {
                setDateOfBirth(e.target.value)
                clearError("dateOfBirth")
              }}
              disabled={loading}
              autoComplete="bday"
              className={errors.dateOfBirth ? "border-red-500" : ""}
            />
            {errors.dateOfBirth && (
              <p className="text-sm text-red-500">{errors.dateOfBirth}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              required
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value)
                clearError("password")
              }}
              disabled={loading}
              autoComplete="new-password"
              className={errors.password ? "border-red-500" : ""}
            />
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              required
              type="password"
              value={confirmPassword}
              onChange={(e) => {
                setConfirmPassword(e.target.value)
                clearError("confirmPassword")
              }}
              disabled={loading}
              autoComplete="new-password"
              className={errors.confirmPassword ? "border-red-500" : ""}
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">{errors.confirmPassword}</p>
            )}
          </div>
          <Button
            className="w-full"
            type="submit"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating account...
              </>
            ) : (
              "Create Account"
            )}
          </Button>
        </form>
        <div className="mt-4 text-center text-sm">
          Already have an account?{" "}
          <Link href="/login" className="text-primary hover:underline">
            Login
          </Link>
        </div>
      </div>
    </div>
  )
}
