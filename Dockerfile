# Production stage
FROM node:23-alpine3.20
WORKDIR /app

# Install dependencies required for Sharp
RUN apk add --no-cache vips-dev build-base python3

# Copy pre-built application
COPY .next/standalone/ ./
COPY .next/standalone/.next/static ./.next/static

# Copy scripts and package files
COPY scripts ./scripts
COPY package.json ./package.json
COPY package-lock.json ./package-lock.json

# Install only production dependencies
RUN npm ci --omit=dev

# Create public directory if it doesn't exist
RUN mkdir -p ./public

# Verify the static files are in place
RUN ls -la ./.next/static || echo "Static files not found!"

EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Start the server
CMD ["node", "server.js"]
