"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface Promotion {
  _id: string
  title: string
  description: string
  price: string
  startDate: string
  endDate: string
  ctaText: string
  ctaLink: string
  isActive: boolean
}

export function ActivePromotions() {
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchPromotions() {
      try {
        const response = await fetch('/api/promotions')
        if (!response.ok) throw new Error('Failed to fetch promotions')
        const data = await response.json()
        setPromotions(data)
      } catch (error) {
        console.error('Error fetching promotions:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPromotions()
  }, [])

  if (loading) {
    return (
      <div className="container py-8">
        <div className="h-32 flex items-center justify-center">
          <p className="text-muted-foreground">Loading promotions...</p>
        </div>
      </div>
    )
  }

  if (promotions.length === 0) {
    return null
  }

  return (
    <section className="container py-8">
      <h2 className="text-3xl font-bold mb-6">Special Offers</h2>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {promotions.map((promo) => (
          <Card key={promo._id} className="flex flex-col">
            <CardHeader>
              <CardTitle>{promo.title}</CardTitle>
              <CardDescription>{promo.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-2xl font-bold">{promo.price}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Valid until: {new Date(promo.endDate).toLocaleDateString()}
              </p>
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <a href={promo.ctaLink}>{promo.ctaText}</a>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </section>
  )
} 