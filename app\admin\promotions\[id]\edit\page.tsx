"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useToast } from "@/components/ui/use-toast"

interface PromotionFormData {
  _id?: string
  title: string
  description: string
  price: string
  startDate: Date
  endDate: Date
  ctaText: string
  ctaLink: string
  isActive: boolean
}

export default function EditPromotionPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<PromotionFormData>({
    title: "",
    description: "",
    price: "",
    startDate: new Date(),
    endDate: new Date(),
    ctaText: "Book Now",
    ctaLink: "/book",
    isActive: true
  })

  useEffect(() => {
    fetchPromotion()
  }, [])

  async function fetchPromotion() {
    try {
      const response = await fetch(`/api/promotions/${params.id}`)
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(errorText || 'Failed to fetch promotion')
      }
      const data = await response.json()
      if (!data) {
        throw new Error('No promotion data received')
      }
      setFormData({
        ...data,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        title: data.title || "",
        description: data.description || "",
        price: data.price || "",
        ctaText: data.ctaText || "Book Now",
        ctaLink: data.ctaLink || "/book",
        isActive: data.isActive ?? true
      })
    } catch (error) {
      console.error('Error fetching promotion:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch promotion",
        variant: "destructive",
      })
      router.push('/admin/promotions')
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)
    try {
      // Remove _id from the form data if it exists
      const { _id, ...updateData } = formData
      
      const response = await fetch(`/api/promotions/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updateData,
          startDate: formData.startDate.toISOString(),
          endDate: formData.endDate.toISOString(),
        }),
      })

      const responseText = await response.text();
      let data;
      try {
        // Try to parse as JSON if possible
        data = JSON.parse(responseText);
      } catch (e) {
        // If not JSON, just use the raw text
        data = responseText;
      }
      
      console.log('API Response:', {
        status: response.status,
        ok: response.ok,
        data
      });

      if (!response.ok) {
        const errorMessage = typeof data === 'object' && data.error 
          ? data.error 
          : (typeof data === 'string' ? data : 'Failed to update promotion');
          
        throw new Error(errorMessage);
      }

      toast({
        title: "Success",
        description: "Promotion updated successfully",
      })
      
      // Wait a bit before redirecting to ensure the toast is shown
      setTimeout(() => {
        router.push('/admin/promotions')
      }, 1000);
      
    } catch (error) {
      console.error('Error updating promotion:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update promotion",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Edit Promotion</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.back()}>Cancel</Button>
        </div>
      </div>
      
      <div className="space-y-4 max-w-2xl mx-auto">
        <div className="grid gap-2">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => setFormData({...formData, title: e.target.value})}
            placeholder="Enter promotion title"
          />
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            placeholder="Enter promotion description"
          />
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="price">Price/Discount</Label>
          <Input
            id="price"
            value={formData.price}
            onChange={(e) => setFormData({...formData, price: e.target.value})}
            placeholder="Enter price or discount"
          />
        </div>
        
        <div className="grid gap-2">
          <Label>Start Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(formData.startDate, "PPP")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={formData.startDate}
                onSelect={(date) => date && setFormData({...formData, startDate: date})}
                initialFocus
                disabled={undefined}
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="grid gap-2">
          <Label>End Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(formData.endDate, "PPP")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={formData.endDate}
                onSelect={(date) => date && setFormData({...formData, endDate: date})}
                initialFocus
                disabled={undefined}
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="ctaText">Button Text</Label>
          <Input
            id="ctaText"
            value={formData.ctaText}
            onChange={(e) => setFormData({...formData, ctaText: e.target.value})}
            placeholder="Enter button text"
          />
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="ctaLink">Button Link</Label>
          <Input
            id="ctaLink"
            value={formData.ctaLink}
            onChange={(e) => setFormData({...formData, ctaLink: e.target.value})}
            placeholder="Enter button link"
          />
        </div>
        
        <div className="flex gap-4 pt-4">
          <Button onClick={handleSubmit} className="flex-1" disabled={loading}>
            {loading ? "Updating..." : "Update Promotion"}
          </Button>
          <Button variant="outline" onClick={() => router.back()} className="flex-1">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  )
} 