"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function HeroSection() {
  // Function to convert MinIO URL to proxy URL
  const getProxyUrl = (url: string) => {
    return `/api/proxy?url=${encodeURIComponent(url)}`
  }

  const logoUrl = getProxyUrl("https://minioapi.realsoftgames.com/jimmys-bali-ink/logo.jpeg")

  return (
    <section className="relative min-h-screen flex">
      {/* Background with logo */}
      <div
        className="absolute inset-0 bg-contain bg-center bg-no-repeat"
        style={{ backgroundImage: `url('${logoUrl}')` }}
      >
        <div className="absolute inset-0 bg-black/80" />
      </div>

      {/* Content Container - positioned differently based on screen size */}
      <div className="relative w-full flex flex-col justify-center px-4 py-8 sm:py-12 sm:px-6 md:px-8 lg:px-12">
        {/* Empty space to push content down on mobile */}
        <div className="h-[30vh] sm:h-[20vh] md:h-0"></div>
        {/* Text content - centered on mobile, left-aligned on larger screens */}
        <div className="max-w-xl mx-auto text-center sm:text-left sm:mx-0 sm:ml-16 md:ml-24 lg:ml-32 text-white">
          <h1 className="text-3xl font-extrabold sm:text-5xl">
            Your Vision
            <strong className="block font-extrabold text-primary">
              Our Artistry
            </strong>
          </h1>

          <p className="mt-4 max-w-lg sm:text-xl/relaxed">
            Experience the perfect blend of creativity and craftsmanship at Jimmy's Bali Ink.
            Let us bring your tattoo dreams to life.
          </p>

          <div className="mt-8 flex flex-wrap gap-4 text-center">
            <Button size="lg" asChild>
              <Link href="/book">Book Appointment</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/gallery">View Our Work</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
