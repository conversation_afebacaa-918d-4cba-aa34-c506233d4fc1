import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sizes } = body

    if (!sizes || !Array.isArray(sizes)) {
      return NextResponse.json(
        { error: 'Invalid sizes data' },
        { status: 400 }
      )
    }

    const db = await connectToDatabase()
    
    // Update each size's order in the database
    const bulkOps = sizes.map((size, index) => ({
      updateOne: {
        filter: { _id: new ObjectId(size._id) },
        update: { 
          $set: { 
            order: index,
            updatedAt: new Date()
          }
        }
      }
    }))

    await db.collection(COLLECTIONS.SIZES).bulkWrite(bulkOps)

    // Fetch the updated sizes to return
    const updatedSizes = await db.collection(COLLECTIONS.SIZES)
      .find({ _id: { $in: sizes.map(s => new ObjectId(s._id)) } })
      .sort({ order: 1 })
      .toArray()

    return NextResponse.json({
      message: 'Size order updated successfully',
      sizes: updatedSizes.map(s => ({
        ...s,
        _id: s._id.toString()
      }))
    })
  } catch (error) {
    console.error('Error updating size order:', error)
    return NextResponse.json(
      { error: 'Failed to update size order' },
      { status: 500 }
    )
  }
} 