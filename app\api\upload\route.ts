import { NextRequest, NextResponse } from 'next/server'
import { uploadFileToS3, deleteFileFromS3, getFileKeyFromUrl } from '@/app/lib/s3-client'

const ALLOWED_TYPES = ['gallery', 'designs', 'appointments', 'contact']

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called')
    const formData = await request.formData()
    console.log('FormData received')
    const file = formData.get('file') as File | null
    const type = formData.get('type') as string | null
    console.log('File:', file?.name, 'Type:', type)

    // Return early with error if no file or type
    if (!file) {
      console.error('No file provided')
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!type) {
      console.error('No type provided')
      return NextResponse.json({ error: 'No type provided' }, { status: 400 })
    }

    if (!type || !ALLOWED_TYPES.includes(type)) {
      return NextResponse.json(
        { error: `Invalid upload type. Must be one of: ${ALLOWED_TYPES.join(', ')}` },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Only image files are allowed' },
        { status: 400 }
      )
    }

    // Generate unique filename with timestamp and random number
    const timestamp = Date.now()
    const randomNum = Math.round(Math.random() * 1E9)
    console.log('Timestamp:', timestamp, 'Random number:', randomNum)

    // Clean up the original filename and ensure it has a valid extension
    const originalName = file.name.toLowerCase().replace(/[^a-z0-9.]/g, '-')
    console.log('Cleaned original name:', originalName)

    // Make sure we have a valid extension
    const fileExtension = originalName.split('.').pop() || ''
    const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']
    const extension = validExtensions.includes(fileExtension) ? fileExtension : 'jpg'

    // Create the final filename
    const fileName = `${timestamp}-${randomNum}-image.${extension}`
    console.log('Final filename:', fileName)

    // Convert file to buffer
    console.log('Converting file to buffer')
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Upload file to MinIO with type as directory
    console.log(`Uploading file to MinIO in directory: ${type}`)
    try {
      const fileUrl = await uploadFileToS3(buffer, fileName, file.type, type)
      console.log('File uploaded successfully to MinIO:', fileUrl)

      // Make sure the URL uses HTTPS for the client and fix any typos
      let clientUrl = fileUrl;

      // Log the original URL for debugging
      console.log(`Original URL from S3 client: ${clientUrl}`);

      // Convert http to https
      if (clientUrl.startsWith('http://')) {
        clientUrl = clientUrl.replace('http://', 'https://');
        console.log(`Converted to HTTPS: ${clientUrl}`);
      }

      // No need to fix typos as we're using the correct domain

      // Make sure the URL is properly formatted
      if (!clientUrl.startsWith('http')) {
        clientUrl = `https://${clientUrl}`;
        console.log(`Added protocol: ${clientUrl}`);
      }

      console.log(`Upload API returning URL: ${clientUrl} for ${type} image`)

      // Return the URL to be stored in the database
      return NextResponse.json({
        success: true,
        filePath: clientUrl
      })
    } catch (uploadError) {
      console.error('Error uploading file to MinIO:', uploadError)
      throw uploadError
    }

  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to upload file' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const path = searchParams.get('path')

    if (!path) {
      return NextResponse.json(
        { error: 'No file path provided' },
        { status: 400 }
      )
    }

    // Extract the file key from the URL or path
    const fileKey = getFileKeyFromUrl(path)

    if (!fileKey) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      )
    }

    console.log('Deleting file from MinIO:', fileKey)

    try {
      // Delete the file from MinIO
      await deleteFileFromS3(fileKey)

      return NextResponse.json({
        success: true,
        message: 'File deleted successfully'
      })
    } catch (deleteError) {
      console.error('Error deleting file from MinIO:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete file from storage' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in DELETE handler:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete file' },
      { status: 500 }
    )
  }
}