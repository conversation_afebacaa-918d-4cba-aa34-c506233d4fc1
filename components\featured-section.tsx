import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface FeaturedWork {
  _id: string
  name: string
  description: string
  price: number
  imageUrl: string
  size: string
  category: string
  type: string
}

interface FeaturedData {
  flash: FeaturedWork[]
  custom: FeaturedWork[]
  coverup: FeaturedWork[]
}

export function FeaturedSection() {
  const [featuredData, setFeaturedData] = useState<FeaturedData>({
    flash: [],
    custom: [],
    coverup: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeaturedWorks = async () => {
      try {
        const response = await fetch('/api/featured')
        if (!response.ok) {
          throw new Error('Failed to fetch featured works')
        }
        const data = await response.json()
        setFeaturedData(data)
      } catch (error) {
        console.error('Error fetching featured works:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedWorks()
  }, [])

  const hasAnyFeaturedWorks = Object.values(featuredData).some(works => works.length > 0)

  return (
    <section className="py-16 bg-black">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-4 text-white">Featured Works</h2>
        <p className="text-gray-400 text-center mb-12 text-lg">
          Browse our latest featured tattoo designs and custom artworks
        </p>

        {!loading && hasAnyFeaturedWorks ? (
          <div className="relative">
            <div className="flex gap-6 overflow-x-auto pb-8 snap-x snap-mandatory">
              {/* Flash Designs */}
              {featuredData.flash.map((work, index) => (
                <div
                  key={work._id?.toString() || index}
                  className="min-w-[300px] w-[300px] snap-center"
                >
                  <div className="relative aspect-square rounded-lg overflow-hidden mb-4">
                    <img
                      src={work.imageUrl}
                      alt={work.name}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <h3 className="text-white font-semibold text-lg">{work.name}</h3>
                  <p className="text-gray-400">{work.description}</p>
                </div>
              ))}

              {/* Custom Works */}
              {featuredData.custom.map((work, index) => (
                <div
                  key={work._id?.toString() || index}
                  className="min-w-[300px] w-[300px] snap-center"
                >
                  <div className="relative aspect-square rounded-lg overflow-hidden mb-4">
                    <img
                      src={work.imageUrl}
                      alt={work.name}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <h3 className="text-white font-semibold text-lg">{work.name}</h3>
                  <p className="text-gray-400">{work.description}</p>
                </div>
              ))}

              {/* Cover-Ups */}
              {featuredData.coverup.map((work, index) => (
                <div
                  key={work._id?.toString() || index}
                  className="min-w-[300px] w-[300px] snap-center"
                >
                  <div className="relative aspect-square rounded-lg overflow-hidden mb-4">
                    <img
                      src={work.imageUrl}
                      alt={work.name}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <h3 className="text-white font-semibold text-lg">{work.name}</h3>
                  <p className="text-gray-400">{work.description}</p>
                </div>
              ))}
            </div>

            {/* Navigation Buttons */}
            <button
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-black/50 p-2 rounded-full text-white hover:bg-black/75 transition-colors"
              aria-label="Previous slide"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-black/50 p-2 rounded-full text-white hover:bg-black/75 transition-colors"
              aria-label="Next slide"
            >
              <ChevronRight className="w-6 h-6" />
            </button>
          </div>
        ) : (
          <div className="text-center py-16 bg-zinc-900 rounded-lg">
            <p className="text-2xl text-gray-300">Coming Soon!</p>
            <p className="mt-2 text-gray-400">Check back later for featured works</p>
          </div>
        )}
      </div>
    </section>
  )
}