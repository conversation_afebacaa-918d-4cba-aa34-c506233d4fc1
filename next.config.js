/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    domains: ['minioapi.realsoftgames.com', 'minio.realsoftgames.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'minioapi.realsoftgames.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'minioapi.realsoftgames.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'minio.realsoftgames.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'minio.realsoftgames.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Increase static page generation timeout
  staticPageGenerationTimeout: 60,
  // Force dynamic rendering for all pages
  reactStrictMode: true,
  // Skip type checking during build
  swcMinify: true,
  // Configure dynamic pages
  experimental: {
    // Skip type checking during build
    serverComponentsExternalPackages: ['mongodb']
  },
  // Skip middleware URL normalization
  skipMiddlewareUrlNormalize: true,
  // Skip trailing slash redirect
  skipTrailingSlashRedirect: true,
  // Set output to export for static site generation
  output: 'standalone',
  // Configure dynamic API routes
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization' },
        ],
      },
    ];
  },
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      crypto: false,
      stream: false,
      buffer: false,
      net: false,
      tls: false,
      fs: false,
      kerberos: false,
      '@mongodb-js/zstd': false,
      '@aws-sdk/credential-providers': false,
      'gcp-metadata': false,
      snappy: false,
      socks: false,
      aws4: false,
      'mongodb-client-encryption': false,
    };
    return config;
  },
  async redirects() {
    return []
  },
  async rewrites() {
    return [
      {
        source: '/designs',
        destination: '/designs/'
      },
      {
        source: '/designs/:path*',
        destination: '/designs/:path*'
      },
      {
        source: '/services',
        destination: '/services/'
      },
      {
        source: '/services/:path*',
        destination: '/services/:path*'
      },
      {
        source: '/services/custom-designs',
        destination: '/services/custom-designs/'
      }
    ]
  }
};

module.exports = nextConfig;
