import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await getDb()

    // Get counts for all collections
    const [
      designs,
      gallery,
      sizes,
      categories,
      tags,
      users,
      appointments,
      promotions
    ] = await Promise.all([
      db.collection(COLLECTIONS.DESIGNS).countDocuments(),
      db.collection(COLLECTIONS.GALLERY).countDocuments(),
      db.collection(COLLECTIONS.SIZES).countDocuments(),
      db.collection(COLLECTIONS.CATEGORIES).countDocuments(),
      db.collection(COLLECTIONS.TAGS).countDocuments(),
      db.collection(COLLECTIONS.USERS).countDocuments(),
      db.collection(COLLECTIONS.APPOINTMENTS).countDocuments(),
      db.collection(COLLECTIONS.PROMOTIONS).countDocuments()
    ])

    return NextResponse.json({
      designs,
      gallery,
      sizes,
      categories,
      tags,
      users,
      appointments,
      promotions
    })
  } catch (error) {
    console.error('Error fetching admin stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
}