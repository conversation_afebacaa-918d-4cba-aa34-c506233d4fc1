import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { ObjectId } from 'mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    // Public access for tags - no authentication required

    const db = await getDb()
    const tags = await db.collection(COLLECTIONS.TAGS)
      .find()
      .sort({ order: 1 })
      .toArray()

    // Format tags for response
    const formattedTags = tags.map(tag => ({
      _id: tag._id.toString(),
      name: tag.name,
      description: tag.description,
      order: tag.order
    }))

    return NextResponse.json(formattedTags)
  } catch (error) {
    console.error('Error fetching tags:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tags' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)

    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await getDb()
    const { name, description } = await request.json()

    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      )
    }

    // Check if tag with same name already exists (case insensitive)
    const existingTag = await db.collection(COLLECTIONS.TAGS).findOne({
      name: { $regex: new RegExp(`^${name}$`, 'i') }
    })
    if (existingTag) {
      return NextResponse.json(
        { error: 'Tag with this name already exists' },
        { status: 400 }
      )
    }

    // Get the highest order number
    const lastTag = await db.collection(COLLECTIONS.TAGS)
      .findOne({}, { sort: { order: -1 } })
    const order = (lastTag?.order || 0) + 1

    // Insert new tag
    const result = await db.collection(COLLECTIONS.TAGS).insertOne({
      name,
      description,
      order,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: {
        userId: authResult.userId,
        role: authResult.role,
        timestamp: new Date().toISOString()
      }
    })

    const newTag = await db.collection(COLLECTIONS.TAGS).findOne(
      { _id: result.insertedId }
    )

    return NextResponse.json({
      ...newTag,
      _id: newTag?._id.toString()
    })
  } catch (error) {
    console.error('Error creating tag:', error)
    return NextResponse.json(
      { error: 'Failed to create tag' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const db = await getDb()
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const { name, description } = await request.json()

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      )
    }

    const result = await db.collection(COLLECTIONS.TAGS).updateOne(
      { _id: new ObjectId(id) },
      {
        $set: {
          name,
          description,
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating tag:', error)
    return NextResponse.json(
      { error: 'Failed to update tag' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const db = await getDb()
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const result = await db.collection(COLLECTIONS.TAGS).deleteOne({
      _id: new ObjectId(id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting tag:', error)
    return NextResponse.json(
      { error: 'Failed to delete tag' },
      { status: 500 }
    )
  }
}