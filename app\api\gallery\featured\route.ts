import { NextRequest, NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { ObjectId } from 'mongodb'

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

interface Tag {
  _id: ObjectId;
  name: string;
}

interface DBImage {
  _id: ObjectId;
  imageUrl: string;
  category: string;
  tags: ObjectId[];
  isFeatured: boolean;
  featuredOrder?: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '12')

    const db = await getDb()

    // Get featured gallery images
    const images = await db.collection(COLLECTIONS.GALLERY)
      .find({ isFeatured: true })
      .sort({ featuredOrder: 1 })
      .limit(limit)
      .toArray() as DBImage[]

    // Get unique tag IDs from all images
    const tagIds = Array.from(new Set(
      images.flatMap(image => (image.tags || []).map((id: string | ObjectId) =>
        id instanceof ObjectId ? id : new ObjectId(id.toString())
      ))
    ))

    // Fetch all tags that are used in the images
    const tags = tagIds.length > 0
      ? await db.collection(COLLECTIONS.TAGS)
          .find({ _id: { $in: tagIds } })
          .toArray() as Tag[]
      : []

    // Create a map of tag IDs to tag documents
    const tagMap = new Map(
      tags.map(tag => [tag._id.toString(), tag])
    )

    // Format the response
    const formattedImages = images.map(image => {
      return {
        ...image,
        _id: image._id.toString(),
        tags: (image.tags || [])
          .map((tagId: string | ObjectId) => {
            const tagIdStr = tagId instanceof ObjectId ? tagId.toString() : tagId.toString()
            const tag = tagMap.get(tagIdStr)
            if (!tag) return null
            return {
              _id: tag._id.toString(),
              name: tag.name
            }
          })
          .filter(Boolean)
      }
    })

    // Create response with CORS headers
    const response = NextResponse.json(formattedImages)
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    return response

  } catch (error) {
    console.error('Error in gallery featured GET:', error)
    const errorResponse = NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    )
    errorResponse.headers.set('Access-Control-Allow-Origin', '*')
    return errorResponse
  }
}

// Add OPTIONS handler for CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 204 })
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  return response
}
