import { NextResponse } from 'next/server'
import { getDb } from '@/app/lib/mongodb'
import { sendEmail } from '@/app/lib/email'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { format } from 'date-fns'

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const db = await getDb()

    // Parse the date string from the request
    const dateString = body.date
    console.log('Original date string from client (bookings):', dateString)

    // Extract the date components from the ISO string
    // The format is YYYY-MM-DDTHH:mm:ss.sssZ
    const dateParts = dateString.split('T')[0].split('-')
    const year = parseInt(dateParts[0])
    const month = parseInt(dateParts[1]) - 1 // Months are 0-indexed in JavaScript
    const day = parseInt(dateParts[2])

    console.log(`Extracted date parts (bookings): year=${year}, month=${month}, day=${day}`)

    // Create a date object with the extracted parts
    // Add 1 to the day to compensate for timezone difference
    // This is a specific fix for the timezone issue between client and server
    const adjustedDay = day + 1
    console.log(`Adjusted day (bookings): ${adjustedDay} (original: ${day})`)

    const bookingDate = new Date(Date.UTC(year, month, adjustedDay))
    console.log('Final booking date (UTC):', bookingDate.toISOString())

    // Create the booking in the database
    const booking = await db.collection(COLLECTIONS.APPOINTMENTS).insertOne({
      serviceType: body.serviceType,
      date: bookingDate,
      time: body.time,
      name: body.name,
      email: body.email,
      phone: body.phone,
      message: body.message,
      createdAt: new Date(),
      updatedAt: new Date()
    })

    // Send confirmation email to client
    await sendEmail({
      to: body.email,
      subject: "Booking Confirmation - Jimmy's Bali Ink",
      html: `
        <h1>Booking Confirmation</h1>
        <p>Dear ${body.name},</p>
        <p>Thank you for booking with Jimmy's Bali Ink. Here are your booking details:</p>
        <ul>
          <li>Service: ${body.serviceType}</li>
          <li>Date: ${format(new Date(body.date), 'dd/MM/yyyy')}</li>
          <li>Time: ${body.time}</li>
          <li>Size: ${body.size}</li>
        </ul>
        <p>We will review your booking and confirm it shortly.</p>
        <p>Best regards,<br>Jimmy's Bali Ink Team</p>
      `
    })

    // Send notification to admin
    await sendEmail({
      to: process.env.ADMIN_EMAIL!,
      subject: "New Booking Request",
      html: `
        <h1>New Booking Request</h1>
        <p>A new booking has been submitted:</p>
        <ul>
          <li>Client: ${body.name}</li>
          <li>Email: ${body.email}</li>
          <li>Phone: ${body.phone}</li>
          <li>Service: ${body.serviceType}</li>
          <li>Date: ${format(new Date(body.date), 'dd/MM/yyyy')}</li>
          <li>Time: ${body.time}</li>
          <li>Size: ${body.size}</li>
          <li>Description: ${body.message}</li>
        </ul>
        <p>Please review and confirm the booking.</p>
      `
    })

    return NextResponse.json(booking)
  } catch (error) {
    console.error('Error creating booking:', error)
    return NextResponse.json(
      { error: 'Failed to create booking' },
      { status: 500 }
    )
  }
}