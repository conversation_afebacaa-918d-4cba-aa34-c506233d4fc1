"use client"

import { useState, useEffect } from "react"
import { GalleryCard } from "@/components/gallery-card"
import { ImageModal } from "@/components/ui/image-modal"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"

interface Tag {
  _id: string
  name: string
}

interface GalleryImage {
  _id: string
  imageUrl: string
  category: string
  tags: Tag[]
  isFeatured: boolean
  featuredOrder?: number
}

export function GallerySection() {
  const [images, setImages] = useState<GalleryImage[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [selectedImageTitle, setSelectedImageTitle] = useState<string>("")

  useEffect(() => {
    const fetchImages = async () => {
      try {
        // For the homepage gallery section, we'll make this public access
        // without requiring authentication
        const response = await fetch('/api/gallery/featured')
        if (!response.ok) {
          throw new Error('Failed to fetch images')
        }
        const data = await response.json()
        setImages(data.sort((a: GalleryImage, b: GalleryImage) =>
          (a.featuredOrder || 0) - (b.featuredOrder || 0)
        ))
      } catch (error) {
        console.error('Error fetching images:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchImages()
  }, [])

  if (loading || images.length === 0) {
    return null
  }

  return (
    <>
      <section className="py-24 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Work</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Browse through our portfolio of custom tattoos and artistic designs
            </p>
          </div>
          <div className="relative">
            <Carousel
              opts={{
                align: "start",
                loop: true,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {images.map((image) => (
                  <CarouselItem key={image._id} className="pl-2 md:pl-4 basis-full sm:basis-1/2 md:basis-1/3">
                    <GalleryCard
                      {...image}
                      onImageClick={() => {
                        setSelectedImage(image.imageUrl)
                        setSelectedImageTitle(image.category)
                      }}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute -left-12 top-1/2 -translate-y-1/2" />
              <CarouselNext className="absolute -right-12 top-1/2 -translate-y-1/2" />
            </Carousel>
          </div>
        </div>
      </section>

      <ImageModal
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        src={selectedImage || ""}
        alt={selectedImageTitle}
      />
    </>
  )
}
