import Image from "next/image"
import { X } from "lucide-react"
import { getImageUrl } from "@/app/lib/file-utils"

interface MediaFile {
  url: string
  previewUrl: string
}

interface ImageGridProps {
  images: MediaFile[]
  onRemove?: (index: number) => void
}

export function ImageGrid({ images, onRemove }: ImageGridProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {images.map((image, index) => {
        // Determine the image source - use preview URL if available, otherwise use the main URL
        const imageSource = image.previewUrl || image.url;
        // Process the URL through our utility function
        const processedUrl = getImageUrl(imageSource);

        console.log(`ImageGrid rendering image ${index + 1}:`, {
          original: imageSource,
          processed: processedUrl,
          directUrl: image.url,
          isPreview: !!image.previewUrl,
          isMinioUrl: imageSource.includes('minioapi') || imageSource.includes('minio.realsoftgames'),
          isAppointmentImage: imageSource.includes('/appointments/')
        });

        // For debugging - log the full URL
        if (imageSource.includes('/appointments/')) {
          console.log(`Appointment image details for ${index + 1}:`, {
            fullUrl: imageSource,
            processedUrl: processedUrl
          });
        }

        return (
          <div
            key={image.url}
            className="relative aspect-square rounded-lg overflow-hidden border bg-muted group"
          >
            <Image
              src={processedUrl}
              alt={`Reference image ${index + 1}`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              unoptimized={true} // Always use unoptimized for uploaded images
              onError={(e) => {
                console.error(`Error loading image ${index + 1}:`, processedUrl);
                // Try with a direct URL as fallback
                const imgElement = e.currentTarget as HTMLImageElement;
                if (image.url && image.url !== imageSource) {
                  console.log(`Trying fallback URL for image ${index + 1}:`, image.url);
                  imgElement.src = getImageUrl(image.url);
                }
              }}
            />
            {/* Hover effect without the View Full Size button */}
            <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity z-10" />

            {onRemove && (
              <button
                type="button"
                onClick={() => onRemove(index)}
                className="absolute top-1 right-1 p-1.5 bg-background/80 rounded-full hover:bg-background shadow-sm z-20"
              >
                <X className="h-4 w-4" />
              </button>
            )}
            <div className="absolute bottom-1 right-1 bg-background/80 px-2 py-0.5 rounded text-xs z-10">
              {index + 1}
            </div>
          </div>
        );
      })}
    </div>
  );
}