"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { Palette } from "lucide-react"

import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  NavigationMenuLink,
} from "@/components/ui/navigation-menu"
import { JWTPayload } from "@/lib/auth"

export function MobileNav() {
  const pathname = usePathname()
  const router = useRouter()
  const [open, setOpen] = React.useState(false)
  const [user, setUser] = React.useState<JWTPayload | null>(null)
  const [loading, setLoading] = React.useState(true)

  React.useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(res => res.json())
      .then(data => {
        if (data.user) {
          setUser(data.user)
        } else {
          localStorage.removeItem('token')
        }
      })
      .catch(() => {
        localStorage.removeItem('token')
      })
      .finally(() => {
        setLoading(false)
      })
    } else {
      setLoading(false)
    }
  }, [pathname])

  const handleLogout = () => {
    localStorage.removeItem('token')
    setUser(null)
    setOpen(false)
    router.push('/login')
    router.refresh()
  }

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 lg:hidden"
        >
          <svg
            strokeWidth="1.5"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
          >
            <path
              d="M3 5H11"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
            <path
              d="M3 12H16"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
            <path
              d="M3 19H21"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
          </svg>
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0">
        <ScrollArea className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
          <div className="flex flex-col space-y-3">
            <Link
              href="/"
              className="flex items-center space-x-2"
              onClick={() => setOpen(false)}
            >
              <Palette className="h-6 w-6" />
              <span className="font-bold">Jimmy&apos;s Bali Ink</span>
            </Link>
            <div className="flex flex-col space-y-2">
              <div className="flex flex-col space-y-3 pt-6">
                <h4 className="text-sm font-medium">Services</h4>
                <div className="grid gap-3">
                  <NavigationMenuLink asChild>
                    <a
                      href="/services/custom-designs"
                      className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                      onClick={() => setOpen(false)}
                    >
                      <Palette className="h-6 w-6" />
                      <div className="mb-2 mt-4 text-lg font-medium">
                        Custom Designs
                      </div>
                      <p className="text-sm leading-tight text-muted-foreground">
                        Unique tattoo designs crafted just for you
                      </p>
                    </a>
                  </NavigationMenuLink>
                  <div className="grid gap-2">
                    <NavigationMenuLink asChild>
                      <a
                        href="/services#flash"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                        onClick={() => setOpen(false)}
                      >
                        <div className="text-sm font-medium leading-none">Flash Tattoos</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Browse our collection of ready-to-ink designs at fixed prices
                        </p>
                      </a>
                    </NavigationMenuLink>
                    <NavigationMenuLink asChild>
                      <a
                        href="/services#cover-ups"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                        onClick={() => setOpen(false)}
                      >
                        <div className="text-sm font-medium leading-none">Cover-ups</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Transform existing tattoos into new artwork
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </div>
                </div>
              </div>
              <div className="flex flex-col space-y-2 pt-6">
                <Link
                  href="/gallery"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setOpen(false)}
                >
                  Gallery
                </Link>
                <Link
                  href="/designs"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setOpen(false)}
                >
                  Designs
                </Link>
                <Link
                  href="/contact"
                  className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                  onClick={() => setOpen(false)}
                >
                  Contact
                </Link>
              </div>
              <div className="flex flex-col space-y-2 pt-6">
                {loading ? (
                  <span className="text-muted-foreground">Loading...</span>
                ) : user ? (
                  <>
                    <div className="flex items-center space-x-2">
                      <span className="text-muted-foreground">Welcome,</span>
                      <span className="font-medium">{user.firstName}</span>
                    </div>
                    {user.role?.toLowerCase() === 'admin' && (
                      <Button variant="ghost" asChild className="justify-start" onClick={() => setOpen(false)}>
                        <Link href="/admin">Admin Dashboard</Link>
                      </Button>
                    )}
                    <Button variant="ghost" onClick={handleLogout} className="justify-start">
                      Logout
                    </Button>
                  </>
                ) : (
                  <Button variant="ghost" asChild className="justify-start" onClick={() => setOpen(false)}>
                    <Link href="/login">Login</Link>
                  </Button>
                )}
                <Button asChild className="justify-start" onClick={() => setOpen(false)}>
                  <Link href="/book">Book Now</Link>
                </Button>
              </div>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  )
}