"use client"

import { useState, useEffect } from "react"
import { Category } from "@/types"
import { ItemManager } from "../shared/ItemManager"
import { ItemDialog } from "../shared/ItemDialog"
import { DeleteConfirmDialog } from "../shared/DeleteConfirmDialog"
import { toast } from "sonner"

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        toast.error('Authentication required')
        return
      }

      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Authentication required')
          return
        }
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to load categories')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setDialogOpen(true)
  }

  const handleDelete = (id: string) => {
    setDeletingId(id)
    setDeleteDialogOpen(true)
  }

  const handleSubmit = async (formData: { name: string; description?: string }) => {
    console.log('Submitting form data:', formData)
    try {
      setSubmitting(true)
      const token = localStorage.getItem('token')
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      if (editingCategory) {
        console.log('Updating category with ID:', editingCategory._id)
        const response = await fetch(`/api/categories?id=${editingCategory._id}`, {
          method: 'PUT',
          headers,
          body: JSON.stringify({
            name: formData.name,
            description: formData.description || ''
          })
        })

        console.log('Update response status:', response.status)

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to update category')
        }

        await fetchCategories()
        toast.success('Category updated successfully')
      } else {
        const response = await fetch('/api/categories', {
          method: 'POST',
          headers,
          body: JSON.stringify(formData)
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to create category')
        }

        const newCategory = await response.json()
        setCategories(prev => [...prev, newCategory].sort((a, b) => a.order - b.order))
        toast.success('Category created successfully')
      }

      setDialogOpen(false)
      setEditingCategory(null)
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save category')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!deletingId) return

    try {
      const token = localStorage.getItem('token')
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      const response = await fetch(`/api/categories?id=${deletingId}`, {
        method: 'DELETE',
        headers
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete category')
      }

      setCategories(categories.filter(category => category._id !== deletingId))
      setDeleteDialogOpen(false)
      setDeletingId(null)
      toast.success('Category deleted successfully')
    } catch (error) {
      console.error('Error deleting category:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete category')
    }
  }

  const handleReorder = async (reorderedCategories: Category[]) => {
    try {
      const response = await fetch('/api/categories/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ categories: reorderedCategories })
      })
      if (!response.ok) throw new Error('Failed to update category order')
      setCategories(reorderedCategories)
    } catch (error) {
      console.error('Error reordering categories:', error)
      // Revert to original order if failed
      fetchCategories()
    }
  }

  return (
    <div className="container mx-auto py-6">
      <ItemManager
        title="Categories"
        description="Manage your design categories"
        items={categories}
        loading={loading}
        onAddClick={() => {
          setEditingCategory(null);
          setDialogOpen(true);
        }}
        onEditClick={handleEdit}
        onDeleteClick={handleDelete}
        onReorder={handleReorder}
        sortable={true}
      />

      <ItemDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSubmit={handleSubmit}
        title={editingCategory ? "Edit Category" : "Add Category"}
        submitLabel={editingCategory ? "Save" : "Add"}
        initialData={editingCategory || undefined}
        loading={submitting}
      />

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Category"
        description="Are you sure you want to delete this category? This action cannot be undone."
      />
    </div>
  )
}