import { jwtVerify, SignJWT } from 'jose'

export interface JWTPayload {
  userId: string
  email: string
  firstName: string
  surname: string
  role: string
  [key: string]: string | number // Add index signature for jose compatibility
}

// Create a Uint8Array of the JWT secret for use with jose
const secret = new TextEncoder().encode(process.env.JWT_SECRET!)

export async function verifyEdgeJWT(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, secret)
    // Verify that all required fields are present
    const requiredFields: (keyof JWTPayload)[] = ['userId', 'email', 'firstName', 'surname', 'role']
    if (requiredFields.every(field => typeof payload[field] === 'string')) {
      return payload as JWTPayload
    }
    return null
  } catch (error) {
    console.error('Edge JWT verification error:', error)
    return null
  }
}

export async function signEdgeJWT(payload: JWTPayload): Promise<string> {
  try {
    const token = await new SignJWT({
      userId: payload.userId,
      email: payload.email,
      firstName: payload.firstName,
      surname: payload.surname,
      role: payload.role
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setExpirationTime('30d')
      .sign(secret)
    return token
  } catch (error) {
    console.error('Edge JWT signing error:', error)
    throw error
  }
} 