import { NextResponse, NextRequest } from 'next/server'
import { ObjectId } from 'mongodb'
import { connectToDatabase } from '@/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

// GET a single promotion
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the ID is a valid ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid promotion ID format' }, { status: 400 })
    }

    const db = await connectToDatabase()
    const collection = db.collection('promotions')
    
    const promotion = await collection.findOne({ _id: new ObjectId(params.id) })

    if (!promotion) {
      return NextResponse.json({ error: 'Promotion not found' }, { status: 404 })
    }

    return NextResponse.json(promotion)
  } catch (error) {
    console.error('Error fetching promotion:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch promotion' },
      { status: 500 }
    )
  }
}

// PATCH/UPDATE a promotion
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // Remove _id from body if it exists to prevent MongoDB error
    const { _id, ...updateData } = body
    
    // Check if the ID is a valid ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid promotion ID format' }, { status: 400 })
    }
    
    const db = await connectToDatabase()
    const collection = db.collection('promotions')
    
    console.log('Updating promotion with ID:', params.id)
    console.log('Update data:', updateData)
    
    // MongoDB driver in v4+ returns different structure
    const result = await collection.findOneAndUpdate(
      { _id: new ObjectId(params.id) },
      { $set: updateData },
      { returnDocument: 'after' }
    )
    
    console.log('MongoDB update result:', JSON.stringify(result, null, 2))
    
    // Check if the result exists
    if (!result) {
      console.log('Result is null or undefined')
      return NextResponse.json({ error: 'Update failed, no result returned' }, { status: 500 })
    }
    
    // For MongoDB v4+ driver, the updated document is in 'value' property
    // For newer versions, it might be directly in the result
    const updatedDoc = result.value || result
    
    if (!updatedDoc) {
      console.log('Document not found for update')
      return NextResponse.json({ error: 'Promotion not found' }, { status: 404 })
    }
    
    console.log('Successfully updated document:', updatedDoc)
    return NextResponse.json(updatedDoc)
  } catch (error) {
    console.error('Error updating promotion:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update promotion' },
      { status: 500 }
    )
  }
}

// DELETE a promotion
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '')
    const authResult = await verifyAuth(token, true)
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    if (!id) {
      return NextResponse.json(
        { error: 'Promotion ID is required' },
        { status: 400 }
      )
    }

    const db = await connectToDatabase()
    const result = await db.collection(COLLECTIONS.PROMOTIONS).deleteOne({
      _id: new ObjectId(id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Promotion not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting promotion:', error)
    return NextResponse.json(
      { error: 'Failed to delete promotion' },
      { status: 500 }
    )
  }
} 