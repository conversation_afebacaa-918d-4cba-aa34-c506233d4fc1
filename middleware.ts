import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { verifyEdgeJWT } from '@/lib/edge-auth'

interface JWTPayload {
  userId: string
  email: string
  firstName: string
  surname: string
  role: string
}

// Paths that don't require authentication
const PUBLIC_PATHS = [
  '/',
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/check-email',
  '/api/auth/verify',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/designs',
  '/gallery',
  '/flash',
  '/custom',
  '/coverups',
  '/aftercare',
  '/contact',
  '/about'
]

// File extensions to ignore
const IGNORED_EXTENSIONS = [
  '.ico',
  '.png',
  '.jpg',
  '.jpeg',
  '.gif',
  '.svg',
  '.css',
  '.js',
  '.json'
]

export async function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl

  // We no longer need to handle data-uploads paths as we're using the standard public path format

  // Special handling for reset password route
  if (pathname === '/reset-password') {
    const token = searchParams.get('token')
    if (token) {
      return NextResponse.next()
    }
  }

  // Check if the path without query parameters is public
  const isPublicPath = PUBLIC_PATHS.some(path => pathname === path)

  // Skip middleware for public paths and static files
  if (isPublicPath || IGNORED_EXTENSIONS.some(ext => pathname.endsWith(ext))) {
    return NextResponse.next()
  }

  // Handle admin routes
  if (pathname.startsWith('/admin')) {
    // Check for access_token in query parameters (for email links)
    const accessToken = searchParams.get('access_token')

    const token = accessToken ||
                 request.cookies.get('token')?.value ||
                 request.headers.get('authorization')?.split(' ')[1]

    if (!token) {
      // Redirect to login for web routes
      if (!pathname.startsWith('/api/')) {
        return NextResponse.redirect(new URL('/login', request.url))
      }
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    try {
      const payload = await verifyEdgeJWT(token)

      if (!payload || payload.role?.toLowerCase() !== 'admin') {
        // Redirect to home for web routes
        if (!pathname.startsWith('/api/')) {
          return NextResponse.redirect(new URL('/', request.url))
        }
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
      }

      // Store the token in a cookie if it came from the Authorization header
      const response = NextResponse.next()
      if (!request.cookies.get('token')) {
        response.cookies.set('token', token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/'
        })
      }
      return response
    } catch (error) {
      console.error('Token verification error:', error)
      // Redirect to login for web routes
      if (!pathname.startsWith('/api/')) {
        return NextResponse.redirect(new URL('/login', request.url))
      }
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  }

  // For API routes that require authentication
  if (pathname.startsWith('/api/') && !PUBLIC_PATHS.includes(pathname)) {
    // Allow public access to GET requests on these endpoints
    if (request.method === 'GET' && (
      pathname.startsWith('/api/gallery') ||
      pathname.startsWith('/api/designs') ||
      pathname.startsWith('/api/categories') ||
      pathname.startsWith('/api/sizes') ||
      pathname.startsWith('/api/tags')
    )) {
      return NextResponse.next()
    }

    const token = request.headers.get('authorization')?.split(' ')[1] ||
                 request.cookies.get('token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    try {
      const payload = await verifyEdgeJWT(token)
      if (!payload) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Add user info to headers for downstream use
      const requestHeaders = new Headers(request.headers)
      requestHeaders.set('x-user-id', payload.userId)
      requestHeaders.set('x-user-email', payload.email)
      requestHeaders.set('x-user-firstname', payload.firstName)
      requestHeaders.set('x-user-surname', payload.surname)
      requestHeaders.set('x-user-role', payload.role)

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  }

  return NextResponse.next()
}

// Configure which routes to run middleware on
export const config = {
  matcher: [
    '/admin/:path*',
    '/api/gallery/:path*',
    '/api/designs/:path*',
    '/api/flash/:path*',
    '/api/upload/:path*',
    '/reset-password/:path*',
    {
      source: '/reset-password',
      has: [
        {
          type: 'query',
          key: 'token'
        }
      ]
    }
  ]
}
