import { NextRequest, NextResponse } from 'next/server'
import { ObjectId } from 'mongodb'
import { connectToDatabase } from '@/lib/mongodb'
import { verifyAuth } from '@/lib/auth'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

interface TagDetail {
  _id: string
  name: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const token = request.headers.get('authorization')?.split(' ')[1]
    const isAuthenticated = await verifyAuth(token)
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await connectToDatabase()
    const design = await db.collection(COLLECTIONS.DESIGNS).findOne({
      _id: new ObjectId(params.id)
    })

    if (!design) {
      return NextResponse.json(
        { error: 'Design not found' },
        { status: 404 }
      )
    }

    // Fetch size details
    let sizeDetails = { _id: '', name: '' }
    if (design.size) {
      try {
        const size = await db.collection(COLLECTIONS.SIZES).findOne({ 
          _id: new ObjectId(design.size)
        })
        if (size) {
          sizeDetails = {
            _id: size._id.toString(),
            name: size.name
          }
        }
      } catch (error) {
        console.error('Error fetching size:', error)
      }
    }

    // Fetch category details
    let categoryDetails = { _id: '', name: '' }
    if (design.category) {
      try {
        const category = await db.collection(COLLECTIONS.CATEGORIES).findOne({ 
          _id: new ObjectId(design.category)
        })
        if (category) {
          categoryDetails = {
            _id: category._id.toString(),
            name: category.name
          }
        }
      } catch (error) {
        console.error('Error fetching category:', error)
      }
    }

    // Fetch tag details
    let tagDetails: TagDetail[] = []
    if (design.tags && Array.isArray(design.tags)) {
      try {
        const tags = await db.collection(COLLECTIONS.TAGS)
          .find({ 
            _id: { 
              $in: design.tags.map(id => 
                typeof id === 'string' ? new ObjectId(id) : id
              )
            } 
          })
          .toArray()
        tagDetails = tags.map(tag => ({
          _id: tag._id.toString(),
          name: tag.name
        }))
      } catch (error) {
        console.error('Error fetching tags:', error)
      }
    }

    return NextResponse.json({
      ...design,
      _id: design._id.toString(),
      size: sizeDetails,
      category: categoryDetails,
      tags: tagDetails,
      status: design.status || 'draft'
    })
  } catch (error) {
    console.error('Error fetching design:', error)
    return NextResponse.json(
      { error: "Error fetching design" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const token = request.headers.get('authorization')?.split(' ')[1]
    const isAuthenticated = await verifyAuth(token)
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await connectToDatabase()
    const { title, description, price, size, category, tags, media, status } = await request.json()

    if (!title || !price || !size || !category || !media || media.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate size exists
    const sizeExists = await db.collection(COLLECTIONS.SIZES).findOne({ _id: new ObjectId(size) })
    if (!sizeExists) {
      return NextResponse.json(
        { error: 'Invalid size' },
        { status: 400 }
      )
    }

    // Validate category exists
    const categoryExists = await db.collection(COLLECTIONS.CATEGORIES).findOne({ _id: new ObjectId(category) })
    if (!categoryExists) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    const result = await db.collection('designs').updateOne(
      { _id: new ObjectId(params.id) },
      {
        $set: {
          title,
          description,
          price: parseFloat(price),
          size: size, // Store size as plain string
          category: category, // Store category as plain string
          tags: tags || [], // Store tags as plain strings
          media,
          status,
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: "Design not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: "Design updated successfully" })
  } catch (error) {
    console.error('Error updating design:', error)
    return NextResponse.json(
      { error: "Error updating design" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const token = request.headers.get('authorization')?.split(' ')[1]
    const isAuthenticated = await verifyAuth(token)
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const db = await connectToDatabase()
    const result = await db.collection('designs').deleteOne({
      _id: new ObjectId(params.id)
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: "Design not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: "Design deleted successfully" })
  } catch (error) {
    console.error('Error deleting design:', error)
    return NextResponse.json(
      { error: "Error deleting design" },
      { status: 500 }
    )
  }
} 