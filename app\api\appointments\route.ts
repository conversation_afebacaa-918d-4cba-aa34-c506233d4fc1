import { NextResponse } from 'next/server'
import clientPromise from '@/lib/mongodb'
import { sendEmail } from '@/app/lib/email'
import { ObjectId } from 'mongodb'
import { STUDIO_INFO } from '@/lib/config'
import { format } from 'date-fns'
import { createEmailAttachment } from '@/app/lib/file-utils'

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Create the appointment in MongoDB
    const client = await clientPromise
    const db = client.db()

    // Format the date as a Date object while preserving the date
    // Parse the date string from the request
    const dateString = body.date
    console.log('Original date string from client:', dateString)

    // Extract the date components from the ISO string
    // The format is YYYY-MM-DDTHH:mm:ss.sssZ
    const dateParts = dateString.split('T')[0].split('-')
    const year = parseInt(dateParts[0])
    const month = parseInt(dateParts[1]) - 1 // Months are 0-indexed in JavaScript
    const day = parseInt(dateParts[2])

    console.log(`Extracted date parts: year=${year}, month=${month}, day=${day}`)

    // Create a date object with the extracted parts
    // Add 1 to the day to compensate for timezone difference
    // This is a specific fix for the timezone issue between client and server
    const adjustedDay = day + 1
    console.log(`Adjusted day: ${adjustedDay} (original: ${day})`)

    const appointmentDate = new Date(Date.UTC(year, month, adjustedDay))
    console.log('Final appointment date (UTC):', appointmentDate.toISOString())

    // Get the size details
    const size = await db.collection('sizes').findOne(
      { _id: new ObjectId(body.size) }
    )

    if (!size) {
      return NextResponse.json(
        { error: 'Invalid size selected' },
        { status: 400 }
      )
    }

    // Verify the category exists
    const category = await db.collection('categories').findOne(
      { _id: new ObjectId(body.serviceType._id) }
    )

    if (!category) {
      return NextResponse.json(
        { error: 'Invalid service type selected' },
        { status: 400 }
      )
    }

    // Check if there's already an appointment at this time
    const existingAppointment = await db.collection('appointments').findOne({
      date: {
        $gte: new Date(Date.UTC(year, month, adjustedDay, 0, 0, 0)),
        $lte: new Date(Date.UTC(year, month, adjustedDay, 23, 59, 59))
      },
      time: body.time,
      status: { $in: ['pending', 'confirmed'] } // Only check pending and confirmed appointments
    })

    if (existingAppointment) {
      return NextResponse.json(
        {
          error: 'This time slot is already booked',
          code: 'TIME_SLOT_UNAVAILABLE',
          existingAppointment: {
            id: existingAppointment._id.toString(),
            status: existingAppointment.status,
            date: format(existingAppointment.date, 'yyyy-MM-dd'),
            time: existingAppointment.time
          }
        },
        { status: 409 } // Conflict status code
      )
    }

    const appointment = {
      serviceType: {
        _id: category._id.toString(),
        name: category.name,
        slug: category.slug
      },
      fullName: body.fullName,
      email: body.email,
      phone: body.phone,
      size: {
        _id: size._id.toString(),
        name: size.name,
        description: size.description
      },
      description: body.description,
      date: appointmentDate,
      time: body.time,
      status: 'pending',
      media: body.media || [], // Add media array
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = await db.collection('appointments').insertOne(appointment)

    // Create Google Calendar event details
    // Parse the time (e.g., "5:00 PM")
    let [timeValue, period] = body.time.split(' ')
    let [hours, minutes] = timeValue.split(':')
    let hour = parseInt(hours)

    // Convert to 24-hour format if needed
    if (period && period.toUpperCase() === 'PM' && hour < 12) {
      hour += 12
    } else if (period && period.toUpperCase() === 'AM' && hour === 12) {
      hour = 0
    }

    console.log(`Parsed time: hour=${hour}, minutes=${parseInt(minutes)}`)

    // For Google Calendar, we need to create a date in the user's local timezone
    // Google Calendar will handle the timezone conversion automatically
    // We need to use the adjusted date (day + 1) to match the actual appointment date

    // Create a date string in ISO format with the local time
    // Format: YYYY-MM-DDTHH:MM:00
    // Use adjustedDay instead of day to ensure the correct date
    const localDateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(adjustedDay).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(parseInt(minutes)).padStart(2, '0')}:00`

    // Create a date object with the local date string
    // This will be interpreted in the local timezone
    const localDate = new Date(localDateString)

    console.log('Local date string for Google Calendar:', localDateString)
    console.log('Local date object for Google Calendar:', localDate.toString())

    // For Google Calendar, we need to specify the timezone
    // Bali is in Asia/Makassar timezone (WITA - UTC+8)
    const timeZone = 'Asia/Makassar'

    // Create start date
    const startDate = localDate

    console.log('Start date for calendar (UTC):', startDate.toISOString())
    console.log('Start date for calendar (local):', startDate.toString())

    // Create end date (1 hour after start)
    const endDate = new Date(startDate.getTime())
    endDate.setHours(startDate.getHours() + 1) // 1 hour duration

    console.log('End date for calendar (UTC):', endDate.toISOString())

    // Format dates for Google Calendar URL
    // Google Calendar requires dates in YYYYMMDDTHHMMSS format without dashes or colons
    // We'll use the local date and add the timezone
    const formatGoogleCalendarDate = (date: Date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = '00'

      return `${year}${month}${day}T${hours}${minutes}${seconds}`
    }

    const gcalStartDate = formatGoogleCalendarDate(startDate)
    const gcalEndDate = formatGoogleCalendarDate(endDate)

    const eventDetails = {
      text: `Tattoo Appointment - ${category.name}`,
      dates: `${gcalStartDate}/${gcalEndDate}`,
      details: `
Service: ${category.name}
Size: ${size.name}
Description: ${body.description || 'No description provided'}
Location: ${STUDIO_INFO.location.name}
Address: ${STUDIO_INFO.getFullAddress()}
Google Maps: ${STUDIO_INFO.location.googleMapsUrl}
      `.trim(),
      location: STUDIO_INFO.getFullAddress()
    }

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventDetails.text)}&dates=${eventDetails.dates}&details=${encodeURIComponent(eventDetails.details)}&location=${encodeURIComponent(eventDetails.location)}&ctz=${encodeURIComponent(timeZone)}`

    // Send confirmation email to client
    const attachments = body.media?.map((image: { url: string }, index: number) =>
      createEmailAttachment(image.url, index, 'reference')
    ) || [];

    // Try to send emails, but don't fail the appointment creation if emails fail
    let emailStatus = true;
    try {
      await sendEmail({
        to: body.email,
        subject: `Appointment Request Received - ${STUDIO_INFO.name}`,
        html: `
          <div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 24px;">
            <h1 style="color: #333; margin-bottom: 24px; text-align: center;">Appointment Request Received</h1>

            <p style="color: #666; margin-bottom: 24px;">
              Dear ${body.fullName},
            </p>

            <p style="color: #666; margin-bottom: 24px;">
              Thank you for requesting an appointment with ${STUDIO_INFO.name}. Your request has been received and is <strong>pending confirmation</strong> by our team. Here are the details you submitted:
            </p>

            <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
              <h3 style="color: #444; margin-bottom: 16px;">Requested Appointment Details</h3>
              <div style="color: #666;">
                <p><strong>Service:</strong> ${category.name}</p>
                <p><strong>Date:</strong> ${format(appointmentDate, 'dd/MM/yyyy')}</p>
                <p><strong>Time:</strong> ${body.time}</p>
                <p><strong>Size:</strong> ${size.name}</p>
                ${body.description ? `<p><strong>Description:</strong> ${body.description}</p>` : ''}
              </div>
            </div>

            ${attachments.length > 0 ? `
              <div style="margin-top: 24px;">
                <h3 style="color: #444; margin-bottom: 16px;">Reference Images</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                  ${attachments.map((attachment: { cid: string }) => `
                    <div style="position: relative;">
                      <img
                        src="cid:${attachment.cid}"
                        alt="Reference Image"
                        style="width: 100%; border-radius: 8px; border: 1px solid #eee;"
                      />
                    </div>
                  `).join('')}
                </div>
              </div>
            ` : ''}

            <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
              <h3 style="color: #444; margin-bottom: 16px;">Studio Location</h3>
              <div style="color: #666;">
                <p>${STUDIO_INFO.location.name}</p>
                <p>${STUDIO_INFO.location.address}</p>
                <p>${STUDIO_INFO.location.area}</p>
                <p>${STUDIO_INFO.location.city}, ${STUDIO_INFO.location.region} ${STUDIO_INFO.location.postalCode}</p>
                <p>${STUDIO_INFO.location.country}</p>
                <p>
                  <a
                    href="${STUDIO_INFO.location.googleMapsUrl}"
                    style="color: #007bff; text-decoration: none;"
                    target="_blank"
                  >
                    View on Google Maps
                  </a>
                </p>
              </div>
            </div>

            <div style="text-align: center; margin-bottom: 24px;">
              <a
                href="${googleCalendarUrl}"
                style="display: inline-block; background: #4CAF50; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none;"
                target="_blank"
              >
                Add to Google Calendar
              </a>
            </div>

            <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
              <h3 style="color: #444; margin-bottom: 16px;">Contact Us</h3>
              <div style="color: #666;">
                <ul style="list-style: none; padding: 0;">
                  <li style="margin-bottom: 8px;">Phone: <a href="tel:${STUDIO_INFO.phone}" style="color: #007bff; text-decoration: none;">${STUDIO_INFO.phone}</a></li>
                  <li style="margin-bottom: 8px;">WhatsApp: <a href="https://wa.me/${STUDIO_INFO.whatsapp.replace(/[^0-9]/g, '')}" style="color: #007bff; text-decoration: none;">${STUDIO_INFO.whatsapp}</a></li>
                  <li style="margin-bottom: 8px;">Email: <a href="mailto:${STUDIO_INFO.email}" style="color: #007bff; text-decoration: none;">${STUDIO_INFO.email}</a></li>
                </ul>
              </div>
            </div>

            <div style="background: #f0f7ff; border-radius: 12px; padding: 24px; margin-bottom: 24px; border: 1px solid #cce5ff;">
              <h3 style="color: #0066cc; margin-bottom: 16px; text-align: center;">What Happens Next?</h3>
              <div style="color: #333;">
                <p style="margin-bottom: 12px;">1. Our team will review your appointment request</p>
                <p style="margin-bottom: 12px;">2. You will receive a confirmation email once your appointment is approved</p>
                <p style="margin-bottom: 12px;">3. If we need to suggest an alternative time or have questions, we'll contact you directly</p>
              </div>
            </div>

            <div style="color: #666; font-size: 14px; text-align: center;">
              <p style="margin-bottom: 12px;">Please note that your appointment is <strong>not confirmed</strong> until you receive our confirmation email.</p>
              <p>Thank you for choosing ${STUDIO_INFO.name}!</p>
            </div>
          </div>
        `,
        attachments
      });

      // Send notification to admin with reference images
      if (process.env.GMAIL_USER) {
        await sendEmail({
          to: process.env.GMAIL_USER,
          subject: "New Appointment Request",
          html: `
            <div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 24px;">
              <h1 style="color: #333; margin-bottom: 24px;">New Appointment Request</h1>

              <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
                <h3 style="color: #444; margin-bottom: 16px;">Client Information</h3>
                <div style="color: #666;">
                  <p><strong>Client:</strong> ${body.fullName}</p>
                  <p><strong>Email:</strong> <a href="mailto:${body.email}" style="color: #007bff; text-decoration: none;">${body.email}</a></p>
                  <p><strong>Phone:</strong> <a href="tel:${body.phone}" style="color: #007bff; text-decoration: none;">${body.phone}</a></p>
                </div>
              </div>

              <div style="background: #f9f9f9; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
                <h3 style="color: #444; margin-bottom: 16px;">Appointment Details</h3>
                <div style="color: #666;">
                  <p><strong>Service:</strong> ${category.name}</p>
                  <p><strong>Date:</strong> ${format(appointmentDate, 'dd/MM/yyyy')}</p>
                  <p><strong>Time:</strong> ${body.time}</p>
                  <p><strong>Size:</strong> ${size.name}</p>
                  ${body.description ? `<p><strong>Description:</strong> ${body.description}</p>` : ''}
                </div>
              </div>

              ${attachments.length > 0 ? `
                <div style="margin-top: 24px;">
                  <h3 style="color: #444; margin-bottom: 16px;">Reference Images</h3>
                  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                    ${attachments.map((attachment: { cid: string }) => `
                      <div style="position: relative;">
                        <img
                          src="cid:${attachment.cid}"
                          alt="Reference Image"
                          style="width: 100%; border-radius: 8px; border: 1px solid #eee;"
                        />
                      </div>
                    `).join('')}
                  </div>
                </div>
              ` : ''}

              <div style="text-align: center; margin-top: 24px; margin-bottom: 24px;">
                <p style="color: #666; margin-bottom: 16px;">Please review and confirm the appointment.</p>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/appointments/${result.insertedId.toString()}" style="display: inline-block; background-color: #007bff; color: white; font-weight: bold; padding: 12px 24px; text-decoration: none; border-radius: 4px; text-align: center;">
                  View Appointment
                </a>
              </div>
            </div>
          `,
          attachments
        });
      }
    } catch (emailError) {
      // Log the error but don't throw it
      console.error('Failed to send email notifications, but appointment was created:', emailError);
      // Set email status to false if there was an error
      emailStatus = false;
    }

    // emailStatus is already set in the try/catch block above

    return NextResponse.json({
      message: 'Appointment request created successfully',
      appointmentId: result.insertedId.toString(),
      emailSent: emailStatus,
      note: emailStatus ? undefined : 'Email notification could not be sent. Please contact the studio directly to confirm your appointment.'
    })
  } catch (error) {
    console.error('Error creating appointment:', error)
    return NextResponse.json(
      { error: 'Failed to create appointment' },
      { status: 500 }
    )
  }
}