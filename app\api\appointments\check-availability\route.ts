import { NextRequest, NextResponse } from 'next/server'
import clientPromise from '@/lib/mongodb'
import { startOfDay, endOfDay } from 'date-fns'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    const time = searchParams.get('time')

    if (!date || !time) {
      return NextResponse.json(
        { error: 'Date and time parameters are required' },
        { status: 400 }
      )
    }

    // Parse the date
    const parsedDate = new Date(date)
    const dayStart = startOfDay(parsedDate)
    const dayEnd = endOfDay(parsedDate)

    // Connect to the database
    const client = await clientPromise
    const db = client.db()

    // Check if there's already an appointment at this time
    const existingAppointment = await db.collection('appointments').findOne({
      date: {
        $gte: dayStart,
        $lte: dayEnd
      },
      time: time,
      status: { $in: ['pending', 'confirmed'] } // Only check pending and confirmed appointments
    })

    return NextResponse.json({
      available: !existingAppointment,
      existingAppointment: existingAppointment ? {
        id: existingAppointment._id.toString(),
        status: existingAppointment.status
      } : null
    })
  } catch (error) {
    console.error('Error checking appointment availability:', error)
    return NextResponse.json(
      { error: 'Failed to check appointment availability' },
      { status: 500 }
    )
  }
}
