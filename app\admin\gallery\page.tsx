"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { useRouter } from 'next/navigation'
import { AdminGalleryCard } from '@/components/admin-gallery-card'
import { connectToDatabase } from '@/lib/mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'
import { ObjectId } from 'mongodb'

interface Tag {
  _id: string
  name: string
}

interface GalleryImage {
  _id: string
  imageUrl: string
  category?: string
  tags: string[]
  isFeatured: boolean
  featuredOrder?: number
}

interface GalleryImageWithTags extends Omit<GalleryImage, 'tags'> {
  tags: Tag[]
}

export default function AdminGalleryPage() {
  const router = useRouter()
  const [images, setImages] = useState<GalleryImageWithTags[]>([])
  const [loading, setLoading] = useState(true)
  const [updatingFeatured, setUpdatingFeatured] = useState<string | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Fetch gallery images first
      const galleryResponse = await fetch('/api/gallery', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!galleryResponse.ok) throw new Error('Failed to fetch gallery')
      const galleryData = await galleryResponse.json()
      console.log('Fetched gallery data:', galleryData)

      // Set the images with their tags
      setImages(galleryData)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const toggleFeatured = async (imageId: string, currentStatus: boolean) => {
    setUpdatingFeatured(imageId)
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/gallery/${imageId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isFeatured: !currentStatus,
          featuredOrder: !currentStatus ? (images.filter(img => img.isFeatured).length) : null
        })
      })

      if (!response.ok) throw new Error('Failed to update image')
      toast.success('Image updated successfully')
      fetchData() // Refresh the list
    } catch (error) {
      console.error('Error updating image:', error)
      toast.error('Failed to update image')
    } finally {
      setUpdatingFeatured(null)
    }
  }

  const deleteImage = async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/gallery/${imageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) throw new Error('Failed to delete image')
      toast.success('Image deleted successfully')
      fetchData() // Refresh the list
    } catch (error) {
      console.error('Error deleting image:', error)
      toast.error('Failed to delete image')
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4">
        <h1 className="text-2xl font-bold">Gallery Management</h1>
        <Button className="w-full sm:w-auto" onClick={() => router.push('/admin/gallery/new')}>
          <Plus className="h-4 w-4 mr-2" />
          <span className="whitespace-nowrap">Add Gallery Image</span>
        </Button>
      </div>

      {/* Featured Images Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Featured Images</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {images
            .filter(image => image.isFeatured)
            .sort((a, b) => (a.featuredOrder || 0) - (b.featuredOrder || 0))
            .map(image => {
              console.log('Rendering featured image:', image)
              return (
                <AdminGalleryCard
                  key={image._id}
                  _id={image._id}
                  imageUrl={image.imageUrl}
                  category={image.category}
                  tags={image.tags}
                  isFeatured={image.isFeatured}
                  updatingFeatured={updatingFeatured === image._id}
                  onToggleFeatured={() => toggleFeatured(image._id, image.isFeatured)}
                  onEdit={() => router.push(`/admin/gallery/${image._id}`)}
                  onDelete={() => deleteImage(image._id)}
                />
              )
            })}
        </div>
      </div>

      {/* All Images Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">All Images</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {images
            .filter(image => !image.isFeatured)
            .map(image => {
              console.log('Rendering non-featured image:', image)
              return (
                <AdminGalleryCard
                  key={image._id}
                  _id={image._id}
                  imageUrl={image.imageUrl}
                  category={image.category}
                  tags={image.tags}
                  isFeatured={image.isFeatured}
                  updatingFeatured={updatingFeatured === image._id}
                  onToggleFeatured={() => toggleFeatured(image._id, image.isFeatured)}
                  onEdit={() => router.push(`/admin/gallery/${image._id}`)}
                  onDelete={() => deleteImage(image._id)}
                />
              )
            })}
        </div>
      </div>
    </div>
  )
}