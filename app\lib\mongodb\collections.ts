// Collection names for MongoDB
export const COLLECTIONS = {
  DESIGNS: 'designs', // Single collection for all design types
  GALLERY: 'gallery',
  SIZES: 'sizes',
  TAGS: 'tags', // Renamed from CATEGORIES
  CATEGORIES: 'categories', // For design categories (Flash, Custom, Cover up)
  USERS: 'users',
  APPOINTMENTS: 'appointments',
  SETTINGS: 'settings',
  PROMOTIONS: 'promotions' // For special offers and promotions
} as const

// Type for collection names
export type CollectionName = keyof typeof COLLECTIONS

// Helper function to get collection name
export function getCollectionName(name: CollectionName): string {
  return COLLECTIONS[name]
}

// Helper function to validate collection name
export function isValidCollection(name: string): name is CollectionName {
  return name in COLLECTIONS
}

// Type for category
export interface Category {
  _id: string
  name: string
  description?: string
  order: number
}

// Type for promotion
export interface Promotion {
  _id: string
  title: string
  description: string
  price: string
  startDate: Date
  endDate: Date
  ctaText: string
  ctaLink: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
} 