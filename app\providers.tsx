"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import { type ThemeProviderProps } from "next-themes/dist/types"
import { Toaster } from "sonner"

export function Providers({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      {...props}
    >
      <Toaster 
        position="bottom-right"
        expand={false}
        richColors
        closeButton
        toastOptions={{
          style: {
            fontSize: '14px',
            padding: '12px',
            maxWidth: '300px'
          }
        }}
      />
      {children}
    </NextThemesProvider>
  )
} 