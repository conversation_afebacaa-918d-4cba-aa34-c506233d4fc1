import { NextResponse } from 'next/server'
import { ObjectId } from 'mongodb'
import { getDb } from '@/app/lib/mongodb'
import { COLLECTIONS } from '@/app/lib/mongodb/collections'

export const runtime = 'nodejs';

// GET /api/promotions - Get all promotions
export async function GET() {
  try {
    const db = await getDb()
    const promotions = await db
      .collection(COLLECTIONS.PROMOTIONS)
      .find({})
      .sort({ createdAt: -1 })
      .toArray()

    return NextResponse.json(promotions)
  } catch (error) {
    console.error('Error fetching promotions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch promotions' },
      { status: 500 }
    )
  }
}

// POST /api/promotions - Create a new promotion
export async function POST(request: Request) {
  try {
    const data = await request.json()

    const promotion = {
      ...data,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const db = await getDb()
    const result = await db
      .collection(COLLECTIONS.PROMOTIONS)
      .insertOne(promotion)

    return NextResponse.json({
      _id: result.insertedId,
      ...promotion
    })
  } catch (error) {
    console.error('Error creating promotion:', error)
    return NextResponse.json(
      { error: 'Failed to create promotion' },
      { status: 500 }
    )
  }
}

// DELETE /api/promotions/[id] - Delete a promotion
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const db = await getDb()
    const result = await db
      .collection(COLLECTIONS.PROMOTIONS)
      .deleteOne({ _id: new ObjectId(id) })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Promotion not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting promotion:', error)
    return NextResponse.json(
      { error: 'Failed to delete promotion' },
      { status: 500 }
    )
  }
}

// PATCH /api/promotions/[id] - Update a promotion
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const data = await request.json()

    const db = await getDb()
    const result = await db
      .collection(COLLECTIONS.PROMOTIONS)
      .updateOne(
        { _id: new ObjectId(id) },
        {
          $set: {
            ...data,
            updatedAt: new Date()
          }
        }
      )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Promotion not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating promotion:', error)
    return NextResponse.json(
      { error: 'Failed to update promotion' },
      { status: 500 }
    )
  }
}