"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { X, ImageOff } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getImageUrl } from "@/app/lib/file-utils"

interface ImageModalProps {
  isOpen: boolean
  onClose: () => void
  src: string
  alt: string
}

export function ImageModal({ isOpen, onClose, src, alt }: ImageModalProps) {
  const [imageError, setImageError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Simplified - now we want all clicks to close the modal
  const handleClick = () => {
    onClose();
  };

  // Use our image utility function to get the correct URL with options
  const imageUrlToUse = getImageUrl(src, {
    cacheBust: retryCount > 0, // Add cache busting if we've retried
    bypassCache: retryCount > 1 // Bypass cache on second retry
  });

  // Handle image loading error
  const handleImageError = () => {
    console.error(`Error loading modal image: ${src}`);

    // Only retry up to 2 times
    if (retryCount < 2) {
      setRetryCount(retryCount + 1);
    } else {
      setImageError(true);
    }
  };

  // Animation variants with different behaviors for overlay and image
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, transition: { duration: 0.4, ease: "easeInOut" } }
  };

  const imageVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 300
      }
    },
    exit: {
      opacity: 1,
      scale: 0.9,
      y: -30,
      transition: {
        duration: 0.35,
        ease: [0.32, 0.72, 0, 1] // Custom cubic bezier for a more dramatic effect
      }
    }
  };

  // Reset error state when modal opens with a new image
  React.useEffect(() => {
    if (isOpen) {
      setImageError(false);
      setRetryCount(0);
    }
  }, [isOpen, src]);

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <Dialog open={isOpen} onOpenChange={onClose}>
          <DialogContent
            className="max-w-[90vw] max-h-[90vh] p-0 bg-transparent border-0 [&>button]:hidden cursor-pointer"
            onClick={handleClick}
            aria-describedby="image-modal-description"
          >
            <DialogTitle className="sr-only">Image Preview</DialogTitle>
            <DialogDescription id="image-modal-description" className="sr-only">
              Click anywhere to close the image preview
            </DialogDescription>
            <motion.button
              onClick={onClose}
              className="absolute top-4 right-4 z-50 p-2.5 rounded-full bg-white/90 hover:bg-white shadow-lg border border-black/10 hover:scale-105 transition-all duration-200"
              initial={{ opacity: 0, scale: 0.8, rotate: -90 }}
              animate={{ opacity: 1, scale: 1, rotate: 0, transition: { delay: 0.1, duration: 0.3 } }}
              exit={{ opacity: 0, scale: 0.8, rotate: 90, transition: { duration: 0.2, ease: "easeInOut" } }}
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 1)" }}
            >
              <X className="h-5 w-5 text-black/80" />
            </motion.button>
            <motion.div
              className="relative w-full h-[80vh] bg-gradient-to-b from-black/60 to-black/40 backdrop-blur-sm"
              variants={overlayVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <motion.div
                className="relative w-auto h-full mx-auto cursor-pointer"
                style={{ maxWidth: '100%' }}
                variants={imageVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <div className="relative w-full h-full overflow-hidden rounded-xl shadow-2xl border-4 border-black/10 bg-black/5">
                  {!imageError ? (
                    <img
                      src={imageUrlToUse}
                      alt={alt}
                      className="absolute inset-0 w-full h-full object-contain rounded-lg"
                      onClick={handleClick}
                      onError={handleImageError}
                    />
                  ) : (
                    <div className="absolute inset-0 w-full h-full flex items-center justify-center bg-gray-800 rounded-lg">
                      <div className="text-center text-gray-400">
                        <ImageOff className="h-16 w-16 mx-auto mb-4" />
                        <p className="text-lg font-medium">Image could not be loaded</p>
                        <p className="text-sm mt-2">Click anywhere to close</p>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          </DialogContent>
        </Dialog>
      )}
    </AnimatePresence>
  )
}