"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, RefreshC<PERSON>, <PERSON><PERSON>, <PERSON>, Clock } from "lucide-react"
import Link from "next/link"
import { ServiceDesigns } from "@/components/service-designs"

export default function ServicesPage() {
  return (
    <div className="container mx-auto py-12 space-y-20">
      {/* Custom Designs Section */}
      <section id="custom-designs" className="scroll-mt-20 space-y-12">
        <div className="grid gap-6 lg:grid-cols-[1fr_400px] items-start">
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Custom Designs
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mt-4">
                Bring your unique vision to life with our custom tattoo design service.
              </p>
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Palette className="w-8 h-8 mb-2" />
                  <CardTitle>Personal Consultation</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    One-on-one sessions to discuss your ideas
                  </CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Star className="w-8 h-8 mb-2" />
                  <CardTitle>Unique Artwork</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    Custom-made designs just for you
                  </CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Clock className="w-8 h-8 mb-2" />
                  <CardTitle>Flexible Scheduling</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    Book sessions at your convenience
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
            <Button asChild size="lg" className="w-full md:w-auto">
              <Link href="/book">Book a Custom Design Session</Link>
            </Button>
          </div>
          <Card className="lg:sticky lg:top-20">
            <CardHeader>
              <CardTitle>Custom Design Process</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">1. Initial Consultation</h4>
                <p className="text-sm text-muted-foreground">
                  Discuss your ideas, placement, and size preferences
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">2. Design Creation</h4>
                <p className="text-sm text-muted-foreground">
                  We create your unique design with revisions if needed
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">3. Final Approval</h4>
                <p className="text-sm text-muted-foreground">
                  Review and approve your custom design
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-4">
          <h3 className="text-2xl font-bold">Custom Design Gallery</h3>
          <ServiceDesigns category="custom" />
        </div>
      </section>

      {/* Flash Designs Section */}
      <section id="flash" className="scroll-mt-20 space-y-12">
        <div className="grid gap-6 lg:grid-cols-[1fr_400px] items-start">
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Flash Designs
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mt-4">
                Pre-made designs ready for immediate tattooing.
              </p>
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Zap className="w-8 h-8 mb-2" />
                  <CardTitle>Ready to Ink</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    Same-day tattoo sessions available
                  </CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Star className="w-8 h-8 mb-2" />
                  <CardTitle>Curated Collection</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    High-quality pre-made designs
                  </CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Clock className="w-8 h-8 mb-2" />
                  <CardTitle>Quick Process</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    No design wait time needed
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
            <Button asChild size="lg" className="w-full md:w-auto">
              <Link href="/designs?category=67ee16db9cc63e740cae6c40">Browse Flash Designs</Link>
            </Button>
          </div>
          <Card className="lg:sticky lg:top-20">
            <CardHeader>
              <CardTitle>Available Styles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">American Traditional</h4>
                <p className="text-sm text-muted-foreground">
                  Bold lines and vibrant colors
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Neo-Traditional</h4>
                <p className="text-sm text-muted-foreground">
                  Modern take on classic designs
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Minimalist</h4>
                <p className="text-sm text-muted-foreground">
                  Simple, elegant designs
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-4">
          <h3 className="text-2xl font-bold">Flash Design Gallery</h3>
          <ServiceDesigns category="flash" />
        </div>
      </section>

      {/* Cover-ups Section */}
      <section id="cover-ups" className="scroll-mt-20 space-y-12">
        <div className="grid gap-6 lg:grid-cols-[1fr_400px] items-start">
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Cover-ups
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mt-4">
                Transform existing tattoos into new, beautiful artwork.
              </p>
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <RefreshCw className="w-8 h-8 mb-2" />
                  <CardTitle>Expert Transformation</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    Skilled cover-up techniques
                  </CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Star className="w-8 h-8 mb-2" />
                  <CardTitle>Fresh Start</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    Turn old into new beautiful art
                  </CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-primary text-primary-foreground">
                <CardHeader>
                  <Palette className="w-8 h-8 mb-2" />
                  <CardTitle>Custom Solutions</CardTitle>
                  <CardDescription className="text-primary-foreground/80">
                    Designs that work with existing ink
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <Button asChild size="lg" className="w-full md:w-auto">
                <Link href="/designs?category=67ee16ef9cc63e740cae6c41">Browse Cover-up Designs</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="w-full md:w-auto">
                <Link href="/book">Schedule a Cover-up Consultation</Link>
              </Button>
            </div>
          </div>
          <Card className="lg:sticky lg:top-20">
            <CardHeader>
              <CardTitle>Cover-up Process</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">1. Assessment</h4>
                <p className="text-sm text-muted-foreground">
                  Evaluate existing tattoo and discuss possibilities
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">2. Design Planning</h4>
                <p className="text-sm text-muted-foreground">
                  Create a design that effectively covers the old tattoo
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">3. Execution</h4>
                <p className="text-sm text-muted-foreground">
                  Professional cover-up with attention to detail
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-4">
          <h3 className="text-2xl font-bold">Cover-up Gallery</h3>
          <ServiceDesigns category="67ee16ef9cc63e740cae6c41" />
        </div>
      </section>
    </div>
  )
}
