"use client"

import { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface Promotion {
  _id: string
  title: string
  description: string
  price?: number
  startDate: Date
  endDate: Date
  ctaText: string
  ctaLink: string
  isActive: boolean
  showAsModal: boolean
}

export function PromoModal() {
  const [modalPromo, setModalPromo] = useState<Promotion | null>(null)
  const [open, setOpen] = useState(false)

  useEffect(() => {
    const fetchModalPromotion = async () => {
      try {
        const response = await fetch("/api/promotions")
        const data = await response.json()
        
        // Find the first active modal promotion
        const activeModalPromo = data.find((promo: Promotion) => {
          const now = new Date()
          const start = new Date(promo.startDate)
          const end = new Date(promo.endDate)
          return promo.isActive && promo.showAsModal && now >= start && now <= end
        })
        
        if (activeModalPromo) {
          setModalPromo(activeModalPromo)
          setOpen(true)
        }
      } catch (error) {
        console.error("Error fetching modal promotion:", error)
      }
    }

    fetchModalPromotion()
  }, [])

  if (!modalPromo) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{modalPromo.title}</DialogTitle>
          <DialogDescription>{modalPromo.description}</DialogDescription>
        </DialogHeader>
        <div className="flex justify-end mt-4">
          <Button
            variant="default"
            className="w-full sm:w-auto"
            asChild
          >
            <Link href={modalPromo.ctaLink}>{modalPromo.ctaText}</Link>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 