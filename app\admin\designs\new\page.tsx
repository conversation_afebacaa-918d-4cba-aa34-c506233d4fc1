"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { toast } from 'sonner'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Loader2, Upload, X, ChevronLeft, ChevronRight } from 'lucide-react'
import { DraggableImageArray } from '../../../components/DraggableImageArray'
import { SelectWithDescription } from '@/components/ui/select-with-description'
import { formatIDR } from '@/lib/utils'

interface Size {
  _id: string
  name: string
  description?: string
  order: number
}

interface Category {
  _id: string
  name: string
  description?: string
  order: number
}

interface Tag {
  _id: string
  name: string
  description?: string
  order: number
}

interface MediaFile {
  url: string
  previewUrl: string
}

export default function NewDesign() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [uploadingImage, setUploadingImage] = useState(false)
  const [sizes, setSizes] = useState<Size[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    size: '',
    category: '',
    tags: [] as string[],
    media: [] as MediaFile[],
    status: 'draft'
  })

  useEffect(() => {
    fetchSizes()
    fetchCategories()
    fetchTags()
  }, [])

  const fetchSizes = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/sizes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch sizes')
      }

      const data = await response.json()
      setSizes(data)
    } catch (error) {
      console.error('Error fetching sizes:', error)
      toast.error('Failed to fetch sizes')
    }
  }

  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to fetch categories')
    }
  }

  const fetchTags = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/tags', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch tags')
      }

      const data = await response.json()
      setTags(data)
    } catch (error) {
      console.error('Error fetching tags:', error)
      toast.error('Failed to fetch tags')
    }
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setUploadingImage(true)

    try {
      const uploadedMedia: MediaFile[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // Validate file type
        if (!file.type.startsWith('image/')) {
          toast.error(`${file.name} is not an image file`)
          continue
        }

        // Create preview
        const previewUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(file)
        })

        // Upload file
        const uploadFormData = new FormData()
        uploadFormData.append('file', file)
        uploadFormData.append('type', 'designs')

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: uploadFormData
        })

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`)
        }

        const data = await response.json()
        uploadedMedia.push({
          url: data.filePath,
          previewUrl
        })
      }

      setFormData(prev => ({
        ...prev,
        media: [...prev.media, ...uploadedMedia]
      }))

      toast.success('Images uploaded successfully')
    } catch (error) {
      console.error('Error uploading images:', error)
      toast.error('Failed to upload one or more images')
    } finally {
      setUploadingImage(false)
    }
  }

  const handleRemoveImage = async (index: number) => {
    try {
      const imageToRemove = formData.media[index]

      // Delete from server
      const response = await fetch(`/api/upload?path=${encodeURIComponent(imageToRemove.url)}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete image from server')
      }

      // Update form state
      setFormData(prev => ({
        ...prev,
        media: prev.media.filter((_, i) => i !== index)
      }))

      // Update current index if needed
      if (currentImageIndex >= formData.media.length - 1) {
        setCurrentImageIndex(Math.max(0, formData.media.length - 2))
      }

      toast.success('Image removed successfully')
    } catch (error) {
      console.error('Error removing image:', error)
      toast.error('Failed to remove image')
    }
  }

  const handleSetPrimary = (index: number) => {
    setFormData(prev => ({
      ...prev,
      media: prev.media.map((item, i) => ({
        ...item,
        isPrimary: i === index
      }))
    }))
  }

  const handleCategoryChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      category: value
    }))
  }

  const handleTagChange = (value: string) => {
    setFormData(prev => {
      const currentTags = prev.tags
      const tagExists = currentTags.includes(value)

      let newTags: string[]
      if (tagExists) {
        // Remove the tag if it's already selected
        newTags = currentTags.filter(tag => tag !== value)
      } else {
        // Add the tag if it's not already selected
        newTags = [...currentTags, value]
      }

      return {
        ...prev,
        tags: newTags
      }
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      if (!formData.title || !formData.price || !formData.size || !formData.category || formData.media.length === 0) {
        throw new Error('Please fill in all required fields and upload at least one image')
      }

      const response = await fetch('/api/designs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('Failed to create design')
      }

      toast.success('Design created successfully')
      router.push('/admin/designs')
    } catch (error) {
      console.error('Error creating design:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create design')
    } finally {
      setLoading(false)
    }
  }

  const handleReorderImages = (newImages: MediaFile[]) => {
    setFormData(prev => ({
      ...prev,
      media: newImages
    }))
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="gap-2"
        >
          <ChevronLeft className="h-4 w-4" /> Back
        </Button>
        <h1 className="text-2xl font-bold">Create New Design</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Media</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col gap-4">
              <Label htmlFor="images">Upload Images</Label>
              <div className="flex items-center gap-4">
                <Input
                  id="images"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  disabled={uploadingImage}
                  className="w-full"
                />
                {uploadingImage && <Loader2 className="h-4 w-4 animate-spin" />}
              </div>
              {formData.media.length > 0 && (
                <DraggableImageArray
                  images={formData.media}
                  onReorder={handleReorderImages}
                  onRemove={handleRemoveImage}
                />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Design Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter design title"
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter design description"
              />
            </div>

            <div>
              <Label htmlFor="price">Price (IDR)</Label>
              <Input
                id="price"
                type="number"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                placeholder="Enter price in IDR"
              />
            </div>

            <div>
              <Label htmlFor="size">Size</Label>
              <SelectWithDescription
                items={sizes}
                value={formData.size}
                onValueChange={(value) => setFormData(prev => ({ ...prev, size: value }))}
                placeholder="Select size"
              />
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <SelectWithDescription
                items={categories}
                value={formData.category}
                onValueChange={handleCategoryChange}
                placeholder="Select category"
              />
            </div>

            <div>
              <Label>Tags</Label>
              <div className="space-y-4">
                <SelectWithDescription
                  items={tags.filter(tag => !formData.tags.includes(tag._id))}
                  onValueChange={handleTagChange}
                  placeholder="Add tags"
                />

                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tagId) => {
                      const tag = tags.find(t => t._id === tagId)
                      if (!tag) return null

                      return (
                        <div
                          key={tagId}
                          className="flex items-center gap-2 bg-secondary px-3 py-1 rounded-full"
                        >
                          <span>{tag.name}</span>
                          <button
                            type="button"
                            onClick={() => handleTagChange(tagId)}
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <div className="flex items-center gap-2 mt-1.5">
                <Switch
                  id="status"
                  checked={formData.status === 'live'}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({
                      ...prev,
                      status: checked ? 'live' : 'draft'
                    }))
                  }
                  className={`
                    ${formData.status === 'live'
                      ? '[&>span]:bg-green-500 [&>span]:shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]'
                      : '[&>span]:bg-red-500 [&>span]:shadow-[0_0_10px_2px_rgba(239,68,68,0.4)]'
                    } bg-zinc-900
                  `}
                />
                <span className="text-sm text-muted-foreground">
                  {formData.status === 'draft' ? 'Draft - Only visible to admins' : 'Live - Visible to everyone'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Button
          type="submit"
          className="w-full"
          disabled={loading || uploadingImage}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Design...
            </>
          ) : (
            'Create Design'
          )}
        </Button>
      </form>
    </div>
  )
}