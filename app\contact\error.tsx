'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function ContactError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Contact error:', error)
  }, [error])

  return (
    <div className="container flex flex-col items-center justify-center min-h-[70vh] text-center px-4">
      <h2 className="text-2xl font-bold mb-4">Contact Page Error</h2>
      <p className="text-muted-foreground mb-6 max-w-md">
        We apologize for the inconvenience. An error occurred while loading the contact page.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button 
          onClick={reset}
          variant="default"
        >
          Try again
        </Button>
        <Button 
          variant="outline"
          asChild
        >
          <Link href="/">
            Return Home
          </Link>
        </Button>
      </div>
    </div>
  )
}
