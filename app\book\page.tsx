"use client"

import { useState, FormE<PERSON>, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Palette, Clock, Info, Mail, Phone, MapPin, Upload, X, Image as ImageIcon, Loader2 } from "lucide-react"
import { AppointmentCalendar, AppointmentCalendarRef } from "@/components/appointment-calendar"
import { SelectWithDescription } from "@/components/ui/select-with-description"
import { STUDIO_INFO } from "@/lib/config"
import Link from "next/link"
import Image from "next/image"
import { ImageGrid } from "@/components/ImageGrid"

interface MediaFile {
  url: string
  previewUrl: string
}

interface AppointmentFormData {
  serviceType: string
  fullName: string
  email: string
  phone: string
  size: string
  description: string
  date: Date | undefined
  time: string | undefined
  media: MediaFile[]
}

interface Size {
  _id: string
  name: string
  description: string
}

interface Category {
  _id: string
  name: string
  description: string
  slug: string
}

export default function BookingPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [uploadingImage, setUploadingImage] = useState(false)
  const [sizes, setSizes] = useState<Size[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const calendarRef = useRef<AppointmentCalendarRef>(null)
  const [formData, setFormData] = useState<AppointmentFormData>({
    serviceType: "",
    fullName: "",
    email: "",
    phone: "",
    size: "",
    description: "",
    date: undefined,
    time: undefined,
    media: []
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch sizes
        const sizesResponse = await fetch('/api/sizes')
        if (!sizesResponse.ok) throw new Error('Failed to fetch sizes')
        const sizesData = await sizesResponse.json()
        setSizes(sizesData)

        // Fetch categories
        const categoriesResponse = await fetch('/api/categories')
        if (!categoriesResponse.ok) throw new Error('Failed to fetch categories')
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData)

        // Set default service type if categories are available
        if (categoriesData.length > 0) {
          setFormData(prev => ({
            ...prev,
            serviceType: categoriesData[0]._id
          }))
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Failed to load form data')
      }
    }

    fetchData()
  }, [])

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setUploadingImage(true)

    try {
      const uploadedMedia: MediaFile[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // Validate file type
        if (!file.type.startsWith('image/')) {
          toast.error(`${file.name} is not an image file`)
          continue
        }

        // Create preview
        const previewUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(file)
        })

        // Upload file
        const uploadFormData = new FormData()
        uploadFormData.append('file', file)
        uploadFormData.append('type', 'appointments')

        try {
          console.log('Uploading file:', file.name)
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: uploadFormData
          })

          if (!response.ok) {
            const errorData = await response.json()
            console.error('Upload error response:', errorData)
            throw new Error(errorData.error || `Failed to upload ${file.name}`)
          }

          const data = await response.json()
          console.log('Upload success response:', data)

          // Log the file path for debugging
          console.log(`Received file path from upload API: ${data.filePath}`)

          uploadedMedia.push({
            url: data.filePath,
            previewUrl
          })

          // Log the media object that was created
          console.log('Created media object:', {
            url: data.filePath,
            hasPreview: !!previewUrl
          })
        } catch (uploadError) {
          console.error('Error in file upload:', uploadError)
          toast.error(`Failed to upload ${file.name}: ${uploadError.message}`)
        }
      }

      if (uploadedMedia.length > 0) {
        setFormData(prev => ({
          ...prev,
          media: [...prev.media, ...uploadedMedia]
        }))
        toast.success('Images uploaded successfully')
      }
    } catch (error) {
      console.error('Error uploading images:', error)
      toast.error('Failed to upload one or more images')
    } finally {
      setUploadingImage(false)
    }
  }

  const handleRemoveImage = async (index: number) => {
    try {
      const imageToRemove = formData.media[index]

      // Delete from server
      const response = await fetch(`/api/upload?path=${encodeURIComponent(imageToRemove.url)}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete image from server')
      }

      // Update form state
      setFormData(prev => ({
        ...prev,
        media: prev.media.filter((_, i) => i !== index)
      }))

      toast.success('Image removed successfully')
    } catch (error) {
      console.error('Error removing image:', error)
      toast.error('Failed to remove image')
    }
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate required fields
      if (!formData.fullName || !formData.email || !formData.phone || !formData.size || !formData.date || !formData.time) {
        throw new Error("Please fill in all required fields")
      }

      const selectedCategory = categories.find(cat => cat._id === formData.serviceType)
      if (!selectedCategory) {
        throw new Error("Please select a valid service type")
      }

      // Check if the time slot is available before submitting
      const dateStr = formData.date?.toISOString().split('T')[0]
      const availabilityResponse = await fetch(`/api/appointments/check-availability?date=${dateStr}&time=${encodeURIComponent(formData.time)}`, {
        method: 'GET',
      })

      const availabilityData = await availabilityResponse.json()

      if (!availabilityData.available) {
        toast.error("This time slot is no longer available. Please select another time.")
        // Refresh the calendar to show updated availability
        setFormData({ ...formData, time: undefined })
        // Refresh the calendar slots
        calendarRef.current?.refreshSlots()
        return
      }

      const response = await fetch('/api/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          serviceType: {
            _id: selectedCategory._id,
            name: selectedCategory.name,
            slug: selectedCategory.slug
          },
          date: formData.date?.toISOString(),
        }),
      })

      if (!response.ok) {
        // Check if it's a time slot conflict
        if (response.status === 409) {
          const errorData = await response.json()
          if (errorData.code === 'TIME_SLOT_UNAVAILABLE') {
            toast.error("This time slot is already booked. Please select another time.")
            // Refresh the calendar to show updated availability
            setFormData({ ...formData, time: undefined })
            // Refresh the calendar slots
            calendarRef.current?.refreshSlots()
            return
          }
        }
        throw new Error("Failed to create appointment")
      }

      const data = await response.json();

      // Show success message
      toast.success("Appointment request submitted successfully!");

      // If email failed to send, show a warning
      if (data.emailSent === false) {
        toast.warning("Your appointment was created, but we couldn't send you a confirmation email. Please save your appointment details.");
      }

      // Navigate to confirmation page with email status and appointment ID
      router.push(`/appointment-confirmation?appointmentId=${data.appointmentId}${data.emailSent === false ? '&emailFailed=true' : ''}`)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to submit appointment request")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Book Your Appointment</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Schedule your tattoo session with our experienced artists
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Appointment Details</CardTitle>
              <CardDescription>
                Fill out the form below to schedule your tattoo session
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <Label>Service Type</Label>
                  <RadioGroup
                    value={formData.serviceType}
                    onValueChange={(value) => setFormData({ ...formData, serviceType: value })}
                    className="grid grid-cols-1 md:grid-cols-3 gap-4"
                  >
                    {categories.map((category) => (
                      <div key={category._id} className="flex items-center space-x-2">
                        <RadioGroupItem value={category._id} id={category.slug} />
                        <Label htmlFor={category.slug} className="flex items-center space-x-2">
                          <span>{category.name}</span>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      placeholder="Your full name"
                      value={formData.fullName}
                      onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Your email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      placeholder="Your phone number"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="size">Tattoo Size</Label>
                    <SelectWithDescription
                      items={sizes}
                      value={formData.size}
                      onValueChange={(value) => setFormData({ ...formData, size: value })}
                      placeholder="Select size"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Design Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your tattoo idea..."
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="min-h-[100px]"
                  />
                </div>

                <AppointmentCalendar
                  ref={calendarRef}
                  selectedDate={formData.date}
                  selectedTime={formData.time}
                  onDateChange={(date) => setFormData({ ...formData, date, time: undefined })}
                  onTimeChange={(time) => setFormData({ ...formData, time })}
                />

                <Card>
                  <CardHeader>
                    <CardTitle>Reference Images</CardTitle>
                    <CardDescription>Upload reference images for your tattoo design</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex flex-col gap-4">
                      <Label htmlFor="images">Upload Images</Label>
                      <div className="flex items-center gap-4">
                        <Input
                          id="images"
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleImageUpload}
                          disabled={uploadingImage}
                          className="w-full"
                        />
                        {uploadingImage && <Loader2 className="h-4 w-4 animate-spin" />}
                      </div>
                      {formData.media.length > 0 && (
                        <ImageGrid
                          images={formData.media}
                          onRemove={handleRemoveImage}
                        />
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Button type="submit" size="lg" className="w-full" disabled={loading || uploadingImage}>
                  {loading ? "Submitting..." : "Schedule Consultation"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
              <CardDescription>
                If you have any questions about scheduling your appointment, please don't hesitate to reach out:
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-start space-x-4">
                <MapPin className="h-5 w-5 text-muted-foreground mt-1" />
                <div>
                  <p className="font-medium">Location</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.name}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.address}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.area}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.city}</p>
                  <p className="text-muted-foreground">{STUDIO_INFO.location.region} {STUDIO_INFO.location.postalCode}</p>
                  <Link
                    href={STUDIO_INFO.location.googleMapsUrl}
                    target="_blank"
                    className="text-primary hover:underline inline-block mt-2"
                  >
                    View on Google Maps
                  </Link>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <Phone className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Phone/WhatsApp</p>
                  <Link
                    href={`tel:${STUDIO_INFO.phone}`}
                    className="text-muted-foreground hover:text-primary"
                  >
                    {STUDIO_INFO.phone}
                  </Link>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Email</p>
                  <Link
                    href={`mailto:${STUDIO_INFO.email}`}
                    className="text-muted-foreground hover:text-primary"
                  >
                    {STUDIO_INFO.email}
                  </Link>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Clock className="h-5 w-5 text-muted-foreground mt-1" />
                <div>
                  <p className="font-medium">Business Hours</p>
                  {STUDIO_INFO.getFormattedBusinessHours().map(({ day, hours }) => (
                    <p key={day} className="text-muted-foreground">
                      {day}: {hours}
                    </p>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <iframe
                src={STUDIO_INFO.location.googleMapsEmbedUrl}
                width="100%"
                height="300"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="rounded-lg"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
