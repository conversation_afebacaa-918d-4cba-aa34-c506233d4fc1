"use client"

import { useState, useEffect, useImperativeHandle, forwardRef } from "react"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { format, addYears, isBefore, startOfDay } from "date-fns"

interface TimeSlot {
  time: string
  available: boolean
}

interface AppointmentCalendarProps {
  selectedDate?: Date
  selectedTime?: string
  onDateChange: (date: Date | undefined) => void
  onTimeChange: (time: string | undefined) => void
  minDate?: Date
  maxDate?: Date
  disabledDays?: (date: Date) => boolean
  onValidationError?: () => void
}

export interface AppointmentCalendarRef {
  refreshSlots: () => void;
}

export const AppointmentCalendar = forwardRef<AppointmentCalendarRef, AppointmentCalendarProps>((
  {
    selectedDate,
    selectedTime,
    onDateChange,
    onTimeChange,
    minDate = new Date(),
    maxDate = addYears(new Date(), 2), // Allow bookings up to 24 months in advance
    disabledDays,
    onValidationError
  },
  ref
) => {
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([])
  const [loadingSlots, setLoadingSlots] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Expose the refreshSlots method via ref
  useImperativeHandle(ref, () => ({
    refreshSlots: () => {
      if (selectedDate) {
        fetchAvailableSlots(selectedDate)
      }
    }
  }))

  // Business hours time slots (9 AM to 10 PM, hourly)
  const defaultTimeSlots = [
    "9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM",
    "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM",
    "5:00 PM", "6:00 PM", "7:00 PM", "8:00 PM",
    "9:00 PM", "10:00 PM"
  ]

  // Function to check if a time slot is in the past
  const isTimeSlotInPast = (date: Date, timeSlot: string) => {
    const now = new Date()
    const isToday = startOfDay(date).getTime() === startOfDay(now).getTime()

    if (!isToday) {
      return false // Only check times for today
    }

    // Parse the time slot (e.g., "9:00 AM" -> Date object for today at 9:00 AM)
    const [time, meridiem] = timeSlot.split(' ')
    const [hours, minutes] = time.split(':')
    let hour = parseInt(hours)

    // Convert to 24-hour format
    if (meridiem === 'PM' && hour !== 12) {
      hour += 12
    } else if (meridiem === 'AM' && hour === 12) {
      hour = 0
    }

    const slotDate = new Date(date)
    slotDate.setHours(hour, parseInt(minutes), 0, 0)

    return isBefore(slotDate, now)
  }

  // Function to normalize time format
  const normalizeTimeFormat = (time: string): string => {
    // First, standardize the format
    let normalizedTime = time.trim().toUpperCase()

    // Remove any extra spaces
    normalizedTime = normalizedTime.replace(/\s+/g, ' ')

    // Ensure proper space before AM/PM
    normalizedTime = normalizedTime.replace(/([AMP])M$/, ' $1M')

    // Handle cases without AM/PM
    if (!normalizedTime.includes('AM') && !normalizedTime.includes('PM')) {
      const [hours, minutes] = normalizedTime.split(':')
      const hour = parseInt(hours)
      const period = hour >= 12 ? 'PM' : 'AM'
      const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
      normalizedTime = `${hour12}:${minutes || '00'} ${period}`
    }

    // Ensure minutes are included
    if (!normalizedTime.includes(':')) {
      normalizedTime = normalizedTime.replace(/ ([AMP]M)/, ':00 $1')
    }

    // Final cleanup to ensure consistent format
    const [timepart, meridiem] = normalizedTime.split(' ')
    const [hours, minutes] = timepart.split(':')
    const hour = parseInt(hours)
    const min = minutes || '00'

    // Ensure proper 12-hour format with leading zeros for single-digit hours
    // This is critical for string comparison with database values
    return `${hour}:${min} ${meridiem}`
  }

  useEffect(() => {
    if (selectedDate) {
      fetchAvailableSlots(selectedDate)
    } else {
      setAvailableSlots([]) // Clear slots when no date is selected
    }
  }, [selectedDate])

  const fetchAvailableSlots = async (date: Date) => {
    setLoadingSlots(true)
    setError(null)
    try {
      const formattedDate = format(date, 'yyyy-MM-dd')
      const response = await fetch(`/api/appointments/available-slots?date=${formattedDate}`)

      if (!response.ok) {
        throw new Error('Failed to fetch available slots')
      }

      const data = await response.json()
      console.log('Raw data from API:', data)

      // Normalize booked slots
      const normalizedBookedSlots = (data.bookedSlots || []).map((slot: string) => {
        const normalized = normalizeTimeFormat(slot)
        console.log('Normalized booked slot:', slot, '->', normalized)
        return normalized
      })

      // Map available slots to our TimeSlot interface
      const slots = defaultTimeSlots.map(time => {
        const normalizedTime = normalizeTimeFormat(time)

        console.log(`Checking availability for time slot: ${time} (normalized: ${normalizedTime})`)

        // Check if this time slot is booked by doing a case-insensitive comparison
        // and also checking for variations in format
        const isBooked = normalizedBookedSlots.some((bookedSlot: string) => {
          // Direct comparison
          if (normalizedTime === bookedSlot) {
            console.log(`MATCH FOUND: ${normalizedTime} === ${bookedSlot} (direct match)`)
            return true;
          }

          // Compare without AM/PM
          const [timeOnly] = normalizedTime.split(' ');
          const [bookedTimeOnly] = bookedSlot.split(' ');
          if (timeOnly === bookedTimeOnly) {
            // If hours and minutes match, check if both are AM or both are PM
            const isAM1 = normalizedTime.includes('AM');
            const isAM2 = bookedSlot.includes('AM');
            if (isAM1 === isAM2) {
              console.log(`MATCH FOUND: ${normalizedTime} matches ${bookedSlot} (time part match)`)
              return true;
            }
          }

          return false;
        });

        const isPast = isTimeSlotInPast(date, time)

        console.log('Checking slot:', {
          original: time,
          normalized: normalizedTime,
          isBooked,
          isPast,
          bookedSlots: normalizedBookedSlots
        })

        return {
          time,
          available: !isBooked && !isPast
        }
      })
      setAvailableSlots(slots)
    } catch (error) {
      console.error('Error fetching slots:', error)
      setError('Could not fetch booked slots. Please try again.')
      // On error, only show future slots but mark them as unavailable for safety
      const slots = defaultTimeSlots.map(time => ({
        time,
        available: false
      }))
      setAvailableSlots(slots)
      toast.error('Could not verify slot availability. Please try again.')
    } finally {
      setLoadingSlots(false)
    }
  }

  // Default disabled days function if none provided
  const defaultDisabledDays = (date: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return date < today || date.getDay() === 0 // Disable past dates and Sundays
  }

  const handleTimeSelect = (time: string) => {
    // Additional check to prevent selecting past time slots
    if (selectedDate && isTimeSlotInPast(selectedDate, time)) {
      toast.error("Cannot select a time slot in the past")
      return
    }

    // Notify parent component to handle validation
    if (onValidationError) {
      onValidationError()
    }
    onTimeChange(time)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label>Preferred Date</Label>
          <Card className="border rounded-lg shadow-sm">
            <CardContent className="p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={onDateChange}
                disabled={disabledDays || defaultDisabledDays}
                fromDate={minDate}
                toDate={maxDate}
                showOutsideDays={false}
                className="rounded-md"
                initialFocus
                fromYear={new Date().getFullYear()}
                toYear={new Date().getFullYear() + 2}
              />
            </CardContent>
          </Card>
        </div>

        <div className="space-y-2">
          <Label>Preferred Time</Label>
          <Card className="border rounded-lg shadow-sm h-[400px]">
            <CardContent className="p-4 h-full">
              {loadingSlots ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center h-full text-destructive">
                  {error}
                </div>
              ) : !selectedDate ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  Please select a date first
                </div>
              ) : availableSlots.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  No available slots for {format(selectedDate, 'MMMM d, yyyy')}
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3 h-full content-start overflow-y-auto p-2">
                  {availableSlots.map((slot) => (
                    <Button
                      key={slot.time}
                      variant={selectedTime === slot.time ? "default" : "outline"}
                      className={`w-full transition-colors ${
                        selectedTime === slot.time
                          ? "bg-primary text-primary-foreground hover:bg-primary/90"
                          : slot.available
                            ? "hover:bg-accent hover:text-accent-foreground"
                            : "opacity-50 cursor-not-allowed"
                      }`}
                      disabled={!slot.available}
                      onClick={() => handleTimeSelect(slot.time)}
                      type="button" // Prevent form submission
                    >
                      {slot.available ? slot.time : "Unavailable"}
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
})