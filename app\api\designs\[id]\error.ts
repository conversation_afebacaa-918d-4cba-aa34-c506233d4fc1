import { NextResponse } from 'next/server'

export function GET() {
  return NextResponse.json(
    { error: 'Design detail API error occurred', message: 'This is a fallback response for design detail API route' },
    { status: 500 }
  )
}

export function POST() {
  return NextResponse.json(
    { error: 'Design detail API error occurred', message: 'This is a fallback response for design detail API route' },
    { status: 500 }
  )
}

export function PUT() {
  return NextResponse.json(
    { error: 'Design detail API error occurred', message: 'This is a fallback response for design detail API route' },
    { status: 500 }
  )
}

export function DELETE() {
  return NextResponse.json(
    { error: 'Design detail API error occurred', message: 'This is a fallback response for design detail API route' },
    { status: 500 }
  )
}
