import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Simple cache to avoid repeated fetches for the same URL
const imageCache = new Map<string, { data: ArrayBuffer, contentType: string, timestamp: number }>();

// Cache expiration time: 1 hour
const CACHE_EXPIRATION = 60 * 60 * 1000;

// Timeout for fetch requests (30 seconds)
const FETCH_TIMEOUT = 30000;

// Maximum number of retries
const MAX_RETRIES = 3;

// Function to clean up old cache entries
function cleanupCache() {
  const now = Date.now();
  for (const [key, value] of imageCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRATION) {
      imageCache.delete(key);
    }
  }
}

// Clean up cache every 5 minutes
setInterval(cleanupCache, 5 * 60 * 1000);

/**
 * Fetch with retry logic and exponential backoff
 * @param url URL to fetch
 * @param options Fetch options
 * @param retries Number of retries left
 * @param baseDelay Base delay for exponential backoff
 * @returns Response object
 */
async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries: number = MAX_RETRIES,
  baseDelay: number = 500
): Promise<Response> {
  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }
    return response;
  } catch (error) {
    if (retries <= 0) {
      throw error;
    }

    // Calculate delay with exponential backoff and jitter
    const delay = baseDelay * Math.pow(2, MAX_RETRIES - retries) * (0.5 + Math.random());
    console.log(`Retry attempt ${MAX_RETRIES - retries + 1} for ${url} after ${delay}ms`);

    // Wait for the calculated delay
    await new Promise(resolve => setTimeout(resolve, delay));

    // Retry the fetch
    return fetchWithRetry(url, options, retries - 1, baseDelay);
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');
    // Get timestamp for cache busting if provided
    const timestamp = searchParams.get('t');
    // Get bypass cache parameter
    const bypassCache = searchParams.get('bypass_cache') === 'true';
    // Get retry count parameter
    const retryCount = parseInt(searchParams.get('retry') || '0', 10);

    if (!url) {
      return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
    }

    // Only allow proxying from approved MinIO domains
    if (!url.includes('minioapi.realsoftgames.com') &&
        !url.includes('minio.realsoftgames.com')) {
      console.error(`Proxy request rejected - Invalid domain: ${url}`);
      return NextResponse.json({ error: 'Invalid URL domain' }, { status: 403 });
    }

    // Create a cache key that includes the timestamp if provided
    const cacheKey = timestamp ? `${url}?t=${timestamp}` : url;

    // Check cache first (unless bypass_cache is true)
    if (!bypassCache && imageCache.has(cacheKey)) {
      const cachedData = imageCache.get(cacheKey)!;
      console.log(`Serving cached image for URL: ${url}`);

      return new NextResponse(cachedData.data, {
        status: 200,
        headers: {
          'Content-Type': cachedData.contentType,
          'Cache-Control': 'public, max-age=86400',
          'Access-Control-Allow-Origin': '*',
          'X-Cache': 'HIT'
        }
      });
    }

    // Ensure URL starts with https://
    let normalizedUrl = url;
    if (!normalizedUrl.startsWith('http')) {
      normalizedUrl = `https://${normalizedUrl}`;
      console.log(`Normalized URL to: ${normalizedUrl}`);
    }

    // Convert http to https for MinIO URLs
    if (normalizedUrl.startsWith('http://') &&
        (normalizedUrl.includes('minioapi.realsoftgames.com') ||
         normalizedUrl.includes('minio.realsoftgames.com'))) {
      normalizedUrl = normalizedUrl.replace('http://', 'https://');
      console.log(`Converted to HTTPS: ${normalizedUrl}`);
    }

    console.log(`Proxying request to: ${normalizedUrl}${timestamp ? ' with cache busting' : ''}`);

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), FETCH_TIMEOUT);

    try {
      // Check if this is an appointment image
      const isAppointmentImage = normalizedUrl.includes('/jimmys-bali-ink/appointments/');
      if (isAppointmentImage) {
        console.log('Processing appointment image URL:', normalizedUrl);
      }

      // Try to fetch directly from the server where MinIO is running
      // This bypasses Cloudflare restrictions
      let serverUrl = normalizedUrl
        .replace('https://minioapi.realsoftgames.com', 'http://192.168.16.2:9000')
        .replace('http://minioapi.realsoftgames.com', 'http://192.168.16.2:9000');

      console.log(`Attempting to fetch from server URL: ${serverUrl}`);

      let response;
      let fetchMethod = 'server'; // Track which fetch method succeeded
      const fetchOptions = {
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      };

      try {
        // First try the direct server URL with retry logic
        response = await fetchWithRetry(serverUrl, fetchOptions, 1); // Only 1 retry for server URL
        console.log(`Direct server fetch succeeded for: ${serverUrl}`);
      } catch (directError) {
        console.log(`Direct server fetch failed: ${directError.message}, trying original URL`);
        fetchMethod = 'original';

        // If that fails, try the original URL with retry logic
        try {
          response = await fetchWithRetry(normalizedUrl, fetchOptions, MAX_RETRIES);
          console.log(`Original URL fetch succeeded for: ${normalizedUrl}`);
        } catch (originalError) {
          console.error(`Original URL fetch failed: ${originalError.message}`);

          // If we've already retried through the proxy, try a direct redirect
          if (retryCount > 0) {
            // Create a fallback URL that bypasses the proxy
            const fallbackUrl = url
              .replace('https://minioapi.realsoftgames.com', 'https://minio.realsoftgames.com')
              .replace('http://minioapi.realsoftgames.com', 'https://minio.realsoftgames.com');

            console.log(`All proxy attempts failed, redirecting to: ${fallbackUrl}`);
            clearTimeout(timeoutId);
            return NextResponse.redirect(fallbackUrl, 302);
          }

          // If this is the first attempt, retry with cache busting
          const retryUrl = `/api/proxy?url=${encodeURIComponent(url)}&t=${Date.now()}&retry=${retryCount + 1}`;
          console.log(`Retrying with cache busting: ${retryUrl}`);
          clearTimeout(timeoutId);
          return NextResponse.redirect(retryUrl, 307); // 307 preserves the method
        }
      }

      clearTimeout(timeoutId);

      // Get the content type from the original response
      const contentType = response.headers.get('content-type') || 'application/octet-stream';
      console.log(`Proxy content type: ${contentType} for URL: ${url} (via ${fetchMethod})`);

      // Get the image data as an array buffer
      const imageData = await response.arrayBuffer();
      console.log(`Proxy received ${imageData.byteLength} bytes for URL: ${url}`);

      // Store in cache
      imageCache.set(cacheKey, {
        data: imageData,
        contentType,
        timestamp: Date.now()
      });

      // Create a new response with the image data and appropriate headers
      const imageResponse = new NextResponse(imageData, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=86400',
          'Access-Control-Allow-Origin': '*',
          'X-Cache': 'MISS'
        }
      });

      return imageResponse;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error(`Proxy fetch error for URL ${url}:`, fetchError);

      // Try to use a fallback approach - direct URL without proxy
      try {
        // Create a fallback URL that bypasses the proxy
        const fallbackUrl = url
          .replace('https://minioapi.realsoftgames.com', 'https://minio.realsoftgames.com')
          .replace('http://minioapi.realsoftgames.com', 'https://minio.realsoftgames.com');

        console.log(`Trying fallback URL: ${fallbackUrl}`);

        // Return a redirect to the fallback URL
        return NextResponse.redirect(fallbackUrl, 302);
      } catch (fallbackError) {
        console.error(`Fallback approach failed:`, fallbackError);

        // Return a more detailed error response
        return NextResponse.json(
          {
            error: fetchError instanceof Error ? fetchError.message : 'Fetch error',
            url: url,
            normalizedUrl: normalizedUrl
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error('Error in proxy route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
