"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { DraggableImageArray } from "../../../components/DraggableImageArray"
import { Loader2, Upload, X, ChevronLeft, ChevronRight } from 'lucide-react'
import { SelectWithDescription } from '@/components/ui/select-with-description'
import { formatIDR } from '@/lib/utils'

interface MediaFile {
  url: string
  previewUrl: string
}

interface DesignMedia extends MediaFile {
  order: number
}

interface Category {
  _id: string
  name: string
  description?: string
  order: number
}

interface Size {
  _id: string
  name: string
  description?: string
  order: number
}

interface Tag {
  _id: string
  name: string
  description?: string
  order: number
}

interface Design {
  _id: string
  title: string
  description?: string
  price: number
  size: {
    _id: string
    name: string
  }
  category: {
    _id: string
    name: string
  }
  tags: {
    _id: string
    name: string
  }[]
  media: DesignMedia[]
  status: 'draft' | 'live'
}

export default function EditDesign({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [uploadingImage, setUploadingImage] = useState(false)
  const [sizes, setSizes] = useState<Size[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [formData, setFormData] = useState<Design>({
    _id: "",
    title: "",
    description: "",
    price: 0,
    size: { _id: "", name: "" },
    category: { _id: "", name: "" },
    tags: [],
    media: [],
    status: 'draft'
  })

  useEffect(() => {
    fetchSizes()
    fetchTags()
    fetchCategories()
    fetchDesign()
  }, [])

  const fetchSizes = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/sizes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch sizes')
      }

      const data = await response.json()
      setSizes(data)
    } catch (error) {
      console.error('Error fetching sizes:', error)
      toast.error('Failed to fetch sizes')
    }
  }

  const fetchTags = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/tags', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch tags')
      }

      const data = await response.json()
      setTags(data)
    } catch (error) {
      console.error('Error fetching tags:', error)
      toast.error('Failed to fetch tags')
    }
  }

  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to fetch categories')
    }
  }

  const fetchDesign = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/designs/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (!response.ok) throw new Error("Failed to fetch design")
      const data = await response.json()
      setFormData(data)
    } catch (error) {
      console.error("Error fetching design:", error)
      toast.error("Failed to load design")
      router.push("/admin/designs")
    }
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setUploadingImage(true)

    try {
      const uploadedMedia: MediaFile[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // Validate file type
        if (!file.type.startsWith('image/')) {
          toast.error(`${file.name} is not an image file`)
          continue
        }

        // Create preview
        const previewUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(file)
        })

        // Upload file
        const uploadFormData = new FormData()
        uploadFormData.append('file', file)
        uploadFormData.append('type', formData.category._id || 'designs')

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: uploadFormData
        })

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`)
        }

        const data = await response.json()
        uploadedMedia.push({
          url: data.filePath,
          previewUrl: previewUrl
        })
      }

      setFormData(prev => ({
        ...prev,
        media: [...prev.media, ...uploadedMedia.map((m, index) => ({
          ...m,
          order: prev.media.length + index
        }))]
      }))

      toast.success('Images uploaded successfully')
    } catch (error) {
      console.error('Error uploading images:', error)
      toast.error('Failed to upload one or more images')
    } finally {
      setUploadingImage(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/designs?id=${params.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          category: formData.category._id,
          size: formData.size._id,
          tags: formData.tags.map(tag => tag._id)
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update design')
      }

      toast.success('Design updated successfully')
      router.push('/admin/designs')
    } catch (error) {
      console.error('Error updating design:', error)
      toast.error('Failed to update design')
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveImage = async (index: number) => {
    try {
      const imageToRemove = formData.media[index]

      // Delete from server
      const response = await fetch(`/api/upload?path=${encodeURIComponent(imageToRemove.url)}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete image from server')
      }

      // Update form state
      setFormData(prev => ({
        ...prev,
        media: prev.media.filter((_, i) => i !== index)
      }))

      toast.success('Image removed successfully')
    } catch (error) {
      console.error('Error removing image:', error)
      toast.error('Failed to remove image')
    }
  }

  const handleCancel = () => {
    router.push('/admin/designs')
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Edit Design</h1>
        <Button variant="outline" onClick={handleCancel}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Designs
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl">
        <div className="space-y-2">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) =>
              setFormData({ ...formData, title: e.target.value })
            }
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="price">Price (IDR)</Label>
          <Input
            id="price"
            type="number"
            value={formData.price}
            onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="size">Size</Label>
          <SelectWithDescription
            items={sizes}
            value={formData.size._id}
            onValueChange={(value) => {
              const size = sizes.find(s => s._id === value)
              if (size) {
                setFormData({ ...formData, size: { _id: size._id, name: size.name } })
              }
            }}
            placeholder="Select size"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <SelectWithDescription
            items={categories}
            value={formData.category._id}
            onValueChange={(value) => {
              const category = categories.find(c => c._id === value)
              if (category) {
                setFormData({ ...formData, category: { _id: category._id, name: category.name } })
              }
            }}
            placeholder="Select category"
          />
        </div>

        <div className="space-y-2">
          <Label>Tags</Label>
          <div className="space-y-4">
            <SelectWithDescription
              items={tags.filter(tag => !formData.tags.some(t => t._id === tag._id))}
              onValueChange={(value) => {
                const tag = tags.find(t => t._id === value)
                if (tag) {
                  setFormData({
                    ...formData,
                    tags: [...formData.tags, { _id: tag._id, name: tag.name }]
                  })
                }
              }}
              placeholder="Add tags"
            />

            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag) => (
                  <div
                    key={tag._id}
                    className="flex items-center gap-2 bg-secondary px-3 py-1 rounded-full"
                  >
                    <span>{tag.name}</span>
                    <button
                      type="button"
                      onClick={() => {
                        setFormData({
                          ...formData,
                          tags: formData.tags.filter(t => t._id !== tag._id)
                        })
                      }}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label>Images</Label>
          <div className="space-y-4">
            <Input
              type="file"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              disabled={uploadingImage}
            />
            {formData.media.length > 0 && (
              <DraggableImageArray
                images={formData.media.map(m => ({ url: m.url, previewUrl: m.url }))}
                onReorder={(newImages) => setFormData({
                  ...formData,
                  media: newImages.map((img, i) => ({
                    ...img,
                    order: i
                  })) as DesignMedia[]
                })}
                onRemove={handleRemoveImage}
              />
            )}
          </div>
        </div>

        <div className="flex items-center justify-between pt-6 border-t">
          <div className="flex items-center gap-2">
            <Switch
              id="status"
              checked={formData.status === 'live'}
              onCheckedChange={(checked) =>
                setFormData(prev => ({
                  ...prev,
                  status: checked ? 'live' : 'draft'
                }))
              }
              className={`
                ${formData.status === 'live'
                  ? '[&>span]:bg-green-500 [&>span]:shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]'
                  : '[&>span]:bg-red-500 [&>span]:shadow-[0_0_10px_2px_rgba(239,68,68,0.4)]'
                } bg-zinc-900
              `}
            />
            <span className="text-sm text-muted-foreground">
              {formData.status === 'draft' ? 'Draft - Only visible to admins' : 'Live - Visible to everyone'}
            </span>
          </div>

          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <span className="animate-spin mr-2">⏳</span>
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}