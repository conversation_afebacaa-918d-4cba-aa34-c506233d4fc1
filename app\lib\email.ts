import nodemailer from 'nodemailer';

// Check for required environment variables
const GMAIL_USER = process.env.GMAIL_USER;
const GMAIL_APP_PASSWORD = process.env.GMAIL_APP_PASSWORD;

let transporter: nodemailer.Transporter | null = null;

// Initialize transporter if credentials are available
if (GMAIL_USER && GMAIL_APP_PASSWORD) {
  transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 465,
    secure: true, // use SSL
    auth: {
      user: GMAIL_USER,
      pass: GMAIL_APP_PASSWORD,
    },
  });
}

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  attachments?: Array<{
    filename: string;
    path: string;
    cid?: string;
  }>;
}

export async function sendEmail({ to, subject, html, attachments }: EmailOptions): Promise<void> {
  if (!transporter) {
    console.error('Email service not configured. Please check GMAIL_USER and GMAIL_APP_PASSWORD environment variables.');
    throw new Error('Email service not configured');
  }

  try {
    // Validate email address
    if (!to || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(to)) {
      throw new Error('Invalid recipient email address');
    }

    const info = await transporter.sendMail({
      from: `"Jimmy's Bali Ink" <${GMAIL_USER}>`,
      to,
      subject,
      html,
      attachments,
    });

    console.log('Email sent successfully:', {
      messageId: info.messageId,
      to,
      subject
    });
  } catch (error) {
    console.error('Failed to send email:', {
      error,
      to,
      subject
    });
    throw error;
  }
} 