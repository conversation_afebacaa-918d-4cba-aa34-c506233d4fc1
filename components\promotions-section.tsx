"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface Promotion {
  title: string
  description: string
  price: string
  ctaText: string
  ctaLink: string
}

const promotions: Promotion[] = [
  {
    title: "Flash Tattoo Bundle",
    description: "Get 3 Flash Tattoos",
    price: "$100",
    ctaText: "Book Now",
    ctaLink: "/book",
  },
  // Add more promotions as needed
]

export function PromotionsSection() {
  return (
    <section className="py-16 bg-black">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white">
            Special Offers
          </h2>
          <p className="mx-auto max-w-[700px] text-gray-400 md:text-xl">
            Take advantage of our limited-time promotions and special deals.
          </p>
        </div>
        <div className="mx-auto grid gap-6 pt-12 sm:grid-cols-2 md:gap-8 lg:grid-cols-3">
          {promotions.map((promo, index) => (
            <Card key={index} className="bg-zinc-900 border-zinc-800">
              <CardHeader>
                <CardTitle className="text-2xl text-white">{promo.title}</CardTitle>
                <CardDescription className="text-gray-400">{promo.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-white mb-4">{promo.price}</div>
                <Button asChild className="w-full">
                  <Link href={promo.ctaLink}>{promo.ctaText}</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
} 